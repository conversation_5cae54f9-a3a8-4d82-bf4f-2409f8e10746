﻿using DevExpress.XtraPrinting.Native.Lines;
using DevExpress.XtraSpellChecker.Parser;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Identity.Web.Resource;
using ProgramadorGeneralBLZ.Server.CustomFilterAttributes;
using ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Command;
using ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Query;
using ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Query;
using ProgramadorGeneralBLZ.Server.MediaTR.Programaciones.Query;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;
using System.IO;
using System.Text;


namespace ProgramadorGeneralBLZ.Server.Controllers
{
    [Authorize]
    [ApiController]
    [Route("[controller]")]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAdB2C:Scopes")]
    //[Authorize(Roles = $"{Roles.Programador},{Roles.Admin}")]
    [ClaimRequirementContainRole("extension_Roles", $"{Roles.Programador},{Roles.Admin},{Roles.Consulta}," +
                                                    $"{Roles.Encargado},{Roles.JefeTurno},{Roles.Rodillos}," +
                                                    $"{Roles.B3},{Roles.B5},{Roles.M4}")]
    public class DatosGeneralesController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<MaquinasController> _logger;
        private readonly IWebHostEnvironment _env;

        public DatosGeneralesController(IMediator mediator, ILogger<MaquinasController> logger, IWebHostEnvironment env)
        {
            _mediator = mediator;
            _logger = logger;
            _env = env;
        }


        [HttpGet("consultaHojalata")]
        public async Task<ListResult<ConsultaHojalataDTO>> GetConsultaHojalata(int idCliente, int largo, int ancho, int espesor)
        {
            var model = await _mediator.Send(new GetGetConsultaHojalataQuery(idCliente, largo, ancho, espesor));
            return model;
        }

        [HttpGet("consultafechas")]
        public async Task<ListResult<PedidoProcesadoDTO>> GetConsultaFechas(int idCliente)
        {
            var model = await _mediator.Send(new GetDatosConsultaFechasQuery(idCliente));
            return model;
        }

        [HttpGet("getdatosproductos")]
        public async Task<ListResult<ProductoDTO>> GetDatosProductos()
        {
            var model = await _mediator.Send(new GetDatosProductoQuery());
            return model;
        }

        [HttpGet("getTintas")]
        public async Task<ListResult<CodtintasDTO>> GetCodTintas()
        {
            var model = await _mediator.Send(new GetDatosCodTintasQuery());
            return model;
        }
        [HttpGet("getcodsaplicaciondropdown")]
        public async Task<ListResult<CodigoAplicacionDTO>> GetCodigosAplicacion(string tipo)
        {
            var model = await _mediator.Send(new GetCodsAplicacionQuery(tipo));
            return model;
        }
        [HttpGet("getclientesdropdown")]
        public async Task<ListResult<ClienteDropdownDTO>> GetClientes(string idPedidoCliente)
        {
            var model = await _mediator.Send(new GetClientesQuery(idPedidoCliente));
            return model;
        }
        [HttpGet]
        public async Task<SingleResult<DatosGeneralesDTO>> GetDatosGeneralesParaPedido()
        {
            var model = await _mediator.Send(new GetDatosGeneralesQuery());
            return model;
        }
        [HttpGet("partes/{idPedido}")]
        public async Task<ListResult<DatosPartesGridDTO>> GetDatosPartes(int idPedido)
        {
            var model = await _mediator.Send(new GetDatosPartesQuery(idPedido));
            return model;
        }
        [HttpGet("limpias/{codBarniz}")]
        public async Task<ListResult<TblLimpiezasDTO>> GetDatosLimpias(int codBarniz)
        {
            var model = await _mediator.Send(new GetDatosLimpiasQuery(codBarniz));
            return model;
        }
        [HttpGet("tipoLimpias")]
        public async Task<ListResult<TblTipoLimpiezaDTO>> GetTipoLimpias()
        {
            var model = await _mediator.Send(new GetDatosTblTipoLimpiasQuery());
            return model;
        }
        [HttpPost("updatedatosmaquina")]
        public async Task<SingleResult<int>> UpdateDatosMaquinas([FromBody] MaquinaDTO newDatosMaquina)
        {
            var model = await _mediator.Send(new UpdateDatosMaquinaCommand(newDatosMaquina));
            return model;
            //return Ok(model);
        }
        [HttpGet("generarDatosEntregas")]
        public async Task<SingleResult<int>> GenerarDatosEntregas()
        {
            var model = await _mediator.Send(new GenerarDatosEntregasCommand());
            return model;
            //return Ok(model);
        }
        [HttpGet("getAuditoriaActualizacionDatosPedido")]
        public async Task<SingleResult<string>> GetAuditoriaActualizacionDatosPedido()
        {
            var model = await _mediator.Send(new GetAuditoriaActualizacionDatosPedidoQuery());
            return model;
        }
        [HttpGet("getConsultaDatosActualizables")]
        public async Task<SingleResult<bool>> GetConsultaDatosActualizables()
        {
            var model = await _mediator.Send(new GetConsultaDatosActualizablesQuery());
            return model;
        }
        [HttpGet("getConsultaPlanchas/{minutos}")]
        public async Task<SingleResult<string>> GetConsultaPlanchas(int minutos)
        {
            var model = await _mediator.Send(new GetConsultaListadoPlanchasByTiempoQuery(minutos));
            return model;
        }
        [HttpGet("getConsultaRevisionProcesos")]
        public async Task<SingleResult<string>> GetConsultaRevisionProcesos()
        {
            var model = await _mediator.Send(new GetConsultaRevisionProcesosQuery());
            return model;
        }
        [HttpGet("getControlActualizacionPedProceso")]
        public async Task<SingleResult<string>> GetControlActualizacionPedProceso()
        {
            var model = await _mediator.Send(new GetControlActualizacionPedProcesoQuery());
            return model;
        }
        [HttpGet("avisos")]
        public async Task<ListResult<TablaAvisosDTO>> GetDatosTablaAvisos()
        {
            var model = await _mediator.Send(new GetDatosTablaAvisosQuery());
            return model;
        }

        [HttpGet("mensajeslog")]
        public async Task<ListResult<ProgramacionesPantallaLogDTO>> GetMensajesLog(
            int idLinea,
            long? idMenorQue,
            int tamanoPagina)
        {
            var model = await _mediator.Send(new GetProgramacionesPantallaLogQuery(idLinea, idMenorQue, tamanoPagina));
            return model;
        }
    }
}
