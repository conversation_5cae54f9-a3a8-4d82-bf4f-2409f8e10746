﻿using System.Security.Claims;
using System.Text.RegularExpressions;
using DevExpress.CodeParser;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.VisualBasic;
using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Data.LitalsaDataWarehouse;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Command;

public class ProgramarPedidosBarnizadoCommand : IRequest<SingleResult<int>>
{

    public ProgramarPedidosBarnizadoCommand(ProgramarPedidoDTO datosBarnizado, ClaimsPrincipal user)
    {
        DatosBarnizado = datosBarnizado;
        User = user;
    }
    public ProgramarPedidoDTO DatosBarnizado { get; set; }
    public ClaimsPrincipal User { get; set; }
}

public class ProgramarPedidosBarnizadoCommandHandler : IRequestHandler<ProgramarPedidosBarnizadoCommand, SingleResult<int>>
{
    private readonly ProgramadorLitalsaContext _programadorLitalsaContext;
    private readonly DatoLita01Context _datoLita01Context;
    private readonly LitalsaDataWarehouseContext _litalsaDataWarehouseContext;
    private readonly IDataManipulationService _dataManipulationService;
    public ProgramarPedidosBarnizadoCommandHandler(ProgramadorLitalsaContext programadorLitalsaContext, IDataManipulationService dataManipulationService, DatoLita01Context datoLita01Context, LitalsaDataWarehouseContext litalsaDataWarehouseContext)
    {
        _programadorLitalsaContext = programadorLitalsaContext;
        _dataManipulationService = dataManipulationService;
        _datoLita01Context = datoLita01Context;
        _litalsaDataWarehouseContext = litalsaDataWarehouseContext;
    }

    public async Task<SingleResult<int>> Handle(ProgramarPedidosBarnizadoCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int>
        {
            Errors = new List<string>(),
            Data = 0
        };
        var pedidoEnCurso = 0;

        try
        {
            var listadoIdPedidos = request.DatosBarnizado.Pedidos.Select(s => s.IdPedido);
            var tablaCodApli = await _programadorLitalsaContext.TablaCodigosPedido
                .Where(o => listadoIdPedidos.Contains(o.Idpedido))
                .ToListAsync(cancellationToken);
            var lastPosicion = _programadorLitalsaContext.TablaProgramacion
                .Where(o => o.Idlinea == request.DatosBarnizado.Maquina.Idmaquina)
                .Max(o => o.Posicion);
            foreach (var p in request.DatosBarnizado.Pedidos)
            {
                pedidoEnCurso = p.IdPedido.Value;
                var tp = await CreateTablaProgramacion(p, request.DatosBarnizado.Maquina, request.DatosBarnizado,
                    tablaCodApli, request.User, lastPosicion, cancellationToken);
                //HACK:A veces se usan las impresoras como barnizadoras (donde la máquina lo permite) y hay que diferenciar estos pedidos
                //ya que si a continuación, en la misma máquina pero usándola como impresora, se programa un pedido nuevo de sólo LITO
                //hay que vigilar que al dar a calcular tiempos, no se cargue los tiempos que se han calculado desde la vista de barnizado

                //Por tanto se ha añadido este campo a la tabla TablaProgramación y
                //sólo se marca cuando estamos en barnizado y la máquina es una impresora

                if (request.DatosBarnizado.Maquina.TipoMaquina == Enums.TipoMaquina.Impresora.ToString())
                {
                    tp.ImpresoraComoBarnizadora = true;
                }

                _programadorLitalsaContext.TablaProgramacion.Add(tp);
                lastPosicion += 10;
                result.Data++;
            }

            //Nos aseguramos que siempre, tras añadir un pedido, se muestre la máquina hasta la última posición, simulando que HASTA = 999999999
            var maquina =
                await _programadorLitalsaContext.Maquinas.SingleAsync(o =>
                    o.Idmaquina == request.DatosBarnizado.Maquina.Idmaquina, cancellationToken);
            maquina.PosicionHasta = 9999999;
            await _programadorLitalsaContext.SaveChangesAsync(cancellationToken);
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: Pedido: {pedidoEnCurso} - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
            return result;
        }

        return result;

    }
    private async Task<TablaProgramacion> CreateTablaProgramacion(DatosGeneralesPedidoDTO p, MaquinaDTO maquina,
        ProgramarPedidoDTO datosBarnizado, List<TablaCodigosPedido> tablaCodApli,
        ClaimsPrincipal claimsPrincipal, int? lastPosicion, CancellationToken cancellationToken)
    {
        var tp = new TablaProgramacion();

        tp.TemperaturaSecado = 0;
        tp.VelocidadMaxima = 0;

        // Initialize TablaProgramacion properties
        tp.Idpedido = p.IdPedido.Value;
        tp.Idaplicacion = p.Idcodigoaplicacion;
        tp.Posicion = (int)(Math.Ceiling((double)lastPosicion / 10) * 10) + 10;
        tp.Idlinea = maquina.Idmaquina;
        tp.HojasAprocesar = p.HojasPedido;

        //tp.HoraComienzoEstimada = DateTime.Now;
        //tp.HoraFinEstimada = DateTime.Now.AddHours(2);
        tp.HoraComienzoEstimada = null;
        tp.HoraFinEstimada = null;
        tp.DuracionEstimada = null;
        //tp.HoraReal = DateTime.Now.TimeOfDay;
        //tp.DiaReal = DateTime.Today.Date;
        tp.VarCambios = 0;
        tp.TiposCambio = "A";
        tp.Orden = 666;//Es un campo para el access para hacer que el formulario impreso saliese en orden
        tp.Revisar = true;
        //Debido a CODAPLI's que no tienen codigos de barniz (xj pase en vacio, secado, etc) se comprueba si el barniz informado
        //existe en CodApli, si no es así se dejará en 0/null
        var codBarniz = p.Idproductoprioritario is null or 0 ? p.CODBARNIZ : p.Idproductoprioritario;
        var existeCodBarniz =
            await _programadorLitalsaContext.TablaProductos.AnyAsync(o => o.Idproducto == codBarniz, cancellationToken);
        tp.Idproducto = existeCodBarniz ? codBarniz : null;
        tp.PesoMin = (float?)p.GRMBAZMIN;
        tp.Peso = p.CODBARNIZ is 150091 or 150082
            ? tp.PesoMin
            : p.Gramajeprioritario is > 0
                ? p.Gramajeprioritario
                : (float?)p.GRMBAZ;
        tp.BarnizNecesario = 666;
        tp.Archivado = false;
        tp.TipoLavada = "??????????";
        tp.CaraAaplicar = p.Posicion;//ej: ep
        tp.Volteado = true;
        tp.AplicacionSimultanea = 0;  //FALTA ???????
        tp.OrdenProcesoAplicacion = 666;
        tp.PasadasAdicionales = 666;
        tp.Programador = claimsPrincipal.Identity?.Name;
        tp.TiempoEstimadoCambio = 666;
        tp.TiempoEstimadoTirada = 666;
        tp.DatosPedido = $"{p.DatosCliente} - {p.Espesor_hjlta}x{p.Ancho_hjlta}x{p.Largo_hjlta}";
        //Los datos los obtenemos del barniz directamente no desde codapli -> barniz.
        tp.Producto = datosBarnizado.Barniz != null
            ? $"{_programadorLitalsaContext.TablaProductos.FirstOrDefault(o => o.Idproducto == datosBarnizado.Barniz.Idproducto)?.Denominacion ?? ""} Peso: {tp.PesoMin} - {tp.Peso} g/m2"
            : string.Empty;

        //$"{_programadorLitalsaContext.TablaProductos.FirstOrDefault(o => o.Idproducto == datosCodigoAplicacion.CodBarniz).Denominacion}";/* Peso: {tp.PesoMin} - {tp.Peso} g/m2";*/


        var obs = await CompletaObservaciones(tp, tp.Idpedido.Value, tp.Idaplicacion.Value, cancellationToken, tp.CaraAaplicar);
        tp.Observaciones = !string.IsNullOrEmpty(p.Estado) && p.Estado.Contains("PDTE HJLTA")
            ? $"PEDIDO SIN HOJALATA ASIGNADA{Environment.NewLine}{obs.Observaciones}"
            : obs.Observaciones;
        tp.Obspaseposterior = obs.Obspaseposterior;
        tp.ObsAlmacen = obs.ObsAlmacen;
        tp.ObsCalidad = obs.ObsCalidad;
        var datosFasesPedido =
           await _dataManipulationService.DevuelveOrdenProcesos(tp.Idpedido.Value, tp.Idaplicacion.Value, 0,
        tp.CaraAaplicar);

        tp.Idaplicacionposterior = datosFasesPedido.CodApliPosterior;
        tp.Posicionaplicacionposterior = datosFasesPedido.CaraPosterior;
        tp.Idaplicacionanterior = datosFasesPedido.CodApliAnterior;
        tp.Posicionaplicacionanterior = datosFasesPedido.CaraAnterior;
        tp.Flejar = !p.Obsflejado.Contains("LUEGO") && tp.Idaplicacionposterior == -1;
        // 14/10/2024
        //Datos a obtener de nueva versión de cálculo de temperatura, usando tabla VelocidadesMaquina
        if (maquina.PosicionTandem == 2 && tp.Idproducto == 110146)
        //&& _programadorLitalsaContext.VelocidadesMaquina.Any(o => tp.Idaplicacion.ToString().StartsWith(o.CodApliMq1Consulta.ToString())))
        {
            var datosVelMaq =
                await _dataManipulationService.GetTemperaturaSecadoV2(tp.Idproducto, p.IdPedido, maquina.Idmaquina, cancellationToken);
            if (datosVelMaq != null)
            {
                tp.TemperaturaSecado = (int?)datosVelMaq.TempTiradaMq2;
                tp.VelocidadMaxima = datosVelMaq.VelTiradaMq2;
            }
        }
        //Para asegurar que encontramos la velocidad de la maquina 1 del tandem, como ahora va por la tabla de velocidades
        //hay que simular que somos la posición 2 del tandem y obtener la velocidad de su registro que dice que es para la maquina 1 del tandem
        if (maquina.PosicionTandem == 1 &&
            _programadorLitalsaContext.VelocidadesMaquina.Any(o => tp.Idaplicacion.ToString().StartsWith(o.CodApliMq1Consulta.ToString())))
        {
            var progLineaSig = await _programadorLitalsaContext.TablaProgramacion
                .Where(o => o.Idpedido == tp.Idpedido && o.Idlinea == maquina.Idmaquina + 1)
                .OrderByDescending(o => o.Idprogramacion)
                .FirstOrDefaultAsync(cancellationToken);

            if (progLineaSig != null && progLineaSig.Idproducto == 110146)
            {
                var datosVelMaq =
                    await _dataManipulationService.GetTemperaturaSecadoV2(110146, tp.Idpedido, maquina.Idmaquina + 1, cancellationToken);
                if (datosVelMaq?.VelTiradaMq1 != null)
                {
                    //temp = (double)datosVelMaq.TempTiradaMq2;
                    tp.VelocidadMaxima = (int)datosVelMaq.VelTiradaMq1;
                }
            }
        }
        if (tp.TemperaturaSecado == 0)
        {
            var temperaturaSecado = await _dataManipulationService.GetTemperaturaSecado(tp.Idproducto, p.IdPedido, maquina, cancellationToken);
            //var temperaturaAux = Convert.ToInt32(tp.Idaplicacion.Value.ToString().Substring(4, 3));
            // 22/07/25 - SI SON PASES DE SECADO, LA TEMPERATURA SE OBTIENE DEL PROPIO CÓDIGO DE APLICACIÓN
            var temperaturaAux = tp.Idaplicacion.Value > 100012000 && tp.Idaplicacion.Value < 100020500
                ? Convert.ToInt32(tp.Idaplicacion.Value.ToString().Substring(4, 3))
                : 0;
            tp.TemperaturaSecado = (int?)Math.Max(temperaturaSecado, temperaturaAux);

        }
        if (tp.VelocidadMaxima == 0)
        {
            tp.VelocidadMaxima = (int?)await _dataManipulationService.GetVelocidadMaxima(tp.Idproducto, maquina.Idmaquina, cancellationToken);
        }

        return tp;
    }



    public async Task<DatosObservacionesDTO> CompletaObservaciones(TablaProgramacion tp, int idPedido, int codigoAplicacion, CancellationToken ca,
        string caraAAplicar = null)
    {
        var res = new DatosObservacionesDTO();
        string cadena;
        // almacenamos el tipo de aplicacion que vamos a dar

        int numProcedencias;


        // tipo de pase: un pase por rayas, por lisa,
        // fondo o con reservas

        // ********MODIF.NUEVOS CÓDIGOS****************************************
        // ******EL PRODUCTO PRIORITARIO ESTARÁ EN LA TABLA DE CODIGOS DE APLICACIÓN POR PEDIDO *******
        // ********************************************************************


        // 22/02/19 ******* CORTE
        if (int.Parse(Strings.Mid(idPedido.ToString(), 3, 1)) == 4)
        {
            // pedido tipo corte
            res.Observaciones = "PEDIDO CORTE";
            return res;
        }

        var hojalata = _dataManipulationService.GetCaracteristicasHojalata(idPedido);

        // devuelve_cara = devuelve_cara_aplicacion(IdPedido, codigo_aplicacion)
        var yaprogramado = await _programadorLitalsaContext.TablaProgramacion.AnyAsync(
            o => o.Idpedido == idPedido && o.Idaplicacion == codigoAplicacion, CancellationToken.None);

        var tipoAplicacion0 = _dataManipulationService.DevuelveDatosAplicacion((int)codigoAplicacion, "NombreAplicacionCliente");

        var cogerde = await _programadorLitalsaContext.Matped.AnyAsync(o => o.Nummpe == idPedido && o.Tipo.Contains("PROCESO"), cancellationToken: ca);

        var datosOrdenProcesos = await _dataManipulationService.DevuelveOrdenProcesos(idPedido, codigoAplicacion, 0, caraAAplicar);
        //ordenproceso = DEVUELVE_ORDEN_PROCESOS(idPedido, codigoAplicacion, totalprocesos, procesoAnterior, procesoPosterior, caraAAplicar);
        var ordenproceso = datosOrdenProcesos.Fase;
        var esprimerproceso = datosOrdenProcesos.FaseAnterior <= 0 && !cogerde;
        var esultimoproceso = datosOrdenProcesos.FasePosterior <= 0;

        cadena = "UN PASE ";
        var pedidoProcesado =
            await _programadorLitalsaContext.PedidoProcesado.FirstOrDefaultAsync(o => o.IdPedido == tp.Idpedido, cancellationToken: ca);
        switch (pedidoProcesado.TipoBarnizado)
        {
            // ******DETERMINAMOS EL TIPO DE BARNIZADO ****************+
            // si son asas
            case "N" when pedidoProcesado.TipoElemento == "A":
                cadena += "RODILLO ASAS ";
                break;
            case "N":
                cadena += "FONDO ";
                break;
            case "L":
                cadena += "";
                break;
            case "T":
                break;
            case "":
                //es el tipo de barnizado de asas, fondo, etc
                cadena += "REVISAR TIPO BARNIZADO ES NULO ";
                break;
        }
        // ********************************************************************************


        // *************MARCAMOS SI ES PASE EXTERIOR O PASE INTERIOR *********************
        // Select Case devuelve_cara_aplicacion(IdPedido, codigo_aplicacion)
        cadena = Strings.Left(caraAAplicar, 1) switch
        {
            "e" => cadena + "EXTERIOR ",
            "i" => cadena + "INTERIOR ",
            _ => cadena
        };
        //CASOS ESPECIALES DE PASE EN VACIO y SECADO
        var nombaz = _programadorLitalsaContext.Codapli.FirstOrDefault(o => o.Codbaz == codigoAplicacion)?.Nombaz ?? string.Empty;

        if (nombaz.ToUpperInvariant().Contains("SECADO"))
            cadena = "UN PASE DE SECADO ";
        else if (nombaz.ToUpperInvariant().Contains("VACIO"))
            cadena = "UN PASE EN VACIO ";


        // ********************************************************************************
        // ************* IDENTIFICACIÓN DE LA HJLTA DIFERENCIAL  *********************
        var obsRayas = pedidoProcesado.Obsrayas ?? "";

        // 03/05/2020 BUSCAMOS SI ES HOJALATA DIFERENCIAL DIRECTAMENTE DE LA CONSULTA ENLAZADA EN FRM_PROGRAMACION_PEDIDO
        var caracteristicasHjlta = _dataManipulationService.GetDatosPedido(idPedido, "DevuelveCaracteristicasHojalata").Text;

        var hojalataDiferencial = _dataManipulationService.GetDatosPedido(idPedido, "DevuelveCaracteristicasHojalata").Text?.Contains('D') ?? false;

        if (hojalataDiferencial & esprimerproceso)
        {
            // es hojalata diferencial, por lo cual debería llevar rayas
            // SI OBS_RAYAS ES NULO LO IDENTIFICAMOS
            if (obsRayas == "")
                cadena += $"{Environment.NewLine} REVISAR OBS RAYAS:NULO";
            //SI es HJLTA diferencial Y pone NO lleva RAYAS lo IDENTIFICAMOS
            if (obsRayas.Contains("O LLEVA RAYAS"))
                cadena += $"{Environment.NewLine} REVISAR OBS RAYAS:HJLTA DIF.";
            if (!obsRayas.Contains("O LLEVA RAYAS"))
                cadena += _dataManipulationService.DevuelvePosicionRayas(caraAAplicar, obsRayas) + Environment.NewLine;
        }
        // ********************************************************************************


        // 25/07/2022, SE ESPECIFICA SI ES SOLO SECADO*********************

        if (tipoAplicacion0 == "SECADO")
            cadena += "SECADO";
        //Indica que ya lleva proceso cuando NO es primerproceso, es tipo 55 (investigar que es el 55) o es un reproceso Y tiene hojas terminadas
        var pattern = "^2.[3|6]....";
        Regex rg = new Regex(pattern);
        var digitoAnyo = pedidoProcesado.IdPedido.ToString().Substring(1, 1);
        var pedidOrigen = rg.IsMatch(pedidoProcesado.IdPedido.Value.ToString())
            ? string.Concat($"2{digitoAnyo}0", pedidoProcesado.IdPedido.ToString().AsSpan(3, 4))
            : "0";
        var datosPedidoOrigen = _programadorLitalsaContext.PedidoProcesado.FirstOrDefault(o => o.IdPedido == int.Parse(pedidOrigen));
        if (!esprimerproceso || pedidoProcesado.TipoHjlta == 55 || (datosPedidoOrigen?.HojasTerminadas ?? 0) > 0)
            cadena += " $$";

        //03/05/2020 BUSCAMOS si ES hojalata DIFERENCIAL directamente DE la CONSULTA enlazada EN FRM_PROGRAMACION_PEDIDO
        var hojalataScroll = caracteristicasHjlta?.ToUpper().Contains("SCROLL") ?? false;
        if (hojalataScroll)
        {
            cadena += $"{Environment.NewLine} SCROLL ";

            if (string.Equals(Strings.Left(caraAAplicar, 1), "E", StringComparison.OrdinalIgnoreCase))
                cadena += "EXTERIOR. ";
            if (string.Equals(Strings.Left(caraAAplicar, 1), "I", StringComparison.OrdinalIgnoreCase))
                cadena += "INTERIOR. ";
        }


        // *************DEFINIMOS LA FORMA DE BARNIZADO DE PUNTA O APAISADO *********************************
        // si son reservas longitudinales hay que ver cómo se procesa si de punta o apaisado
        // en el caso de hacerlo de punta se indica. Utilizamos para ello la función devuelve_forma_barnizado
        // If Form_Frm_programacion_pedido!TipoBarnizado = "L" Then
        var formaBarnizado = await _dataManipulationService.DevuelveFormaBarnizado(pedidoProcesado.IdCliente.Value, pedidoProcesado.Plano,
            pedidoProcesado.TipoElemento, pedidoProcesado.Formato.Value, pedidoProcesado.AnchoHjlta.Value, pedidoProcesado.LargoHjlta.Value,
            pedidoProcesado.IdPedido.Value);

        cadena += formaBarnizado;


        //'End If
        //'****************************************************************************************************

        //'*************SI ES UN RODILLO ESPECIAL: TIPO ??L?-1? o -2? *********************************
        //'si es un rodillo especial y es para esmalte tipo 11?? lo indicamos en las observaciones
        Regex regex = new(@"^[^-]*L\d(?!\d)[^-]*-[12].+$");// Calculado con https://regex101.com/
        if (regex.IsMatch(pedidoProcesado.Plano) && codigoAplicacion.ToString().StartsWith("620"))
        {
            cadena += $"{Environment.NewLine}RODILLO ESPECIAL ";
        }

        //'10/04/21 PARA LOS CASOS EN LOS QUE TENGAMOS RODILLOS ESPECIALES INCLUIDOS EN LA TABLA_CFG REGISTRO RODILLOS ESPECIALES
        if (!string.IsNullOrEmpty(pedidoProcesado.Plano) && pedidoProcesado.Plano != "-" && _programadorLitalsaContext.TablaCfg.FirstOrDefault(o => o.Iddato == "RodillosEspeciales").Dato.Contains(pedidoProcesado.Plano))
        {
            cadena += $"{Environment.NewLine}RODILLO ESPECIAL ";
        }

        //'*****10/02/2022, NESTLE LA PENILLA RODILLO ESPECIAL 099L3-12;13 ***********************
        if ((pedidoProcesado.Plano.Contains("099L3-13;12") || pedidoProcesado.Plano.Contains("099L3-12;13"))
            && codigoAplicacion.ToString().StartsWith("620")
            && pedidoProcesado.IdCliente == 9)
        {
            cadena += $"{Environment.NewLine} TIRAR DE PUNTA. OJO EN LITO VA APAISADO, NO DE PUNTA. ";
        }

        if (pedidoProcesado.Plano.Contains("216L1-01") && pedidoProcesado.IdCliente == 3)
        {
            cadena += $"{Environment.NewLine}PALLETS DE PUNTA, LOS TACOS VAN PARALELOS AL 800.";
        }

        if (pedidoProcesado.Plano.Contains("T094L7-01") && pedidoProcesado.IdCliente == 2)
        {
            cadena += $"{Environment.NewLine}TIRAR DE PUNTA. TACOS PALLET PARALELOS AL 883.";
        }

        // marca en observaciones si hay más de una procedencia de hjlta limpia
        // si es el primer proceso y hay varias procedencias lo marca.
        // If esprimerproceso = -1 Or esprimerproceso = 2 Then

        if (esprimerproceso || ordenproceso == 0)
        {
            numProcedencias = _programadorLitalsaContext.Matped.Count(o => o.Nummpe == idPedido);

            if (numProcedencias > 1)
                cadena += $"{Environment.NewLine} HAY " + numProcedencias + " PROCEDENCIAS.";

        }

        if (ordenproceso == 0)
            // no está definido el orden de procesos
            cadena += $"{Environment.NewLine} ***ORDEN DE PROCESOS SIN DEFINIR ***" + Environment.NewLine;


        // **********************SI ES HOJALATA DE CALVO EXPORT, MANTENER TABLA*********************************
        if (pedidoProcesado.IdCliente == 77)
            cadena += " TABLAS ORIGINALES. ";

        // 21/11/18**********************SI ES HOJALATA DE CALVO JUNTAR PICOS *********************************
        if (esprimerproceso && pedidoProcesado.IdCliente == 76 && !hojalata.StartsWith("966x1049"))
        {
            cadena += " SI HAY PICOS DE HOJALATA, JUNTARLOS.";
        }

        // 21/11/18**********************SI ES HOJALATA DE CALVO DE 1049x966x16 JUNTAR PICOS, PAQUETES COMO MINIMO A 1.300 HOJAS*********************************
        if (esprimerproceso && pedidoProcesado.IdCliente == 76 && hojalata.StartsWith("966x1049"))
        {
            cadena += "PAQUETES COMO MÍNIMO A 1.300 HOJAS Y UN PICO FINAL SI NECESARIO.";
        }

        // 12/04/2022**********************SI ES HOJALATA DE CANTÁBRICO SE PUEDEN JUNTAR PICOS *********************************
        if (esprimerproceso && pedidoProcesado.IdCliente == 45)
        {
            cadena += " SI HAY PICOS DE HOJALATA, JUNTARLOS.";
        }
        // ********************************************************************************

        // 07/06/2019 SE ELIMINAN TODAS LAS REFERENCIAS A LOS BARNICES Y SE PASAN A LA TABLA TABLACOMENTARIOS


        // **********************SI SON OROS Y LLEVAN LUEGO ENGANCHE: OJO CUCHILLA.... *********************************
        if (codigoAplicacion.ToString().StartsWith("1") && pedidoProcesado.Pe1ped.ToString().StartsWith("5"))
        {
            cadena += $"{Environment.NewLine}OJO CUCHILLA ENGANCHE EXT.";
        }

        // **********************SI YA SE HA TIRADO ALGUNA HOJA DE ESE PROCESO LO INDICAMOS*************
        var hojasYaTiradas = _programadorLitalsaContext.HojasTrabajoG21
            .Where(o => o.Orden == pedidoProcesado.IdPedido && o.Dato == codigoAplicacion.ToString() && o.Operacion == "10")?.Sum(s => s.Cantidad) ?? 0;

        if (hojasYaTiradas != 0)
            cadena = "YA TIRADAS #" + Strings.Format(hojasYaTiradas, "0,0") + "# HOJAS. " + cadena;
        // ********************************************************************************

        // 26/5/2017***********PARA MUSELET SI ES ESMALTE PONER QUE RODILLO SIN TRANSVERSAL, COLOCAR RASQUETAS EN EL LATERAL.*********************************
        if (pedidoProcesado.IdCliente == 117 && codigoAplicacion.ToString().StartsWith("9601"))
        {
            cadena += " RODILLO SIN TRANSVERSAL, COLOCAR RASQUETAS EN EL LATERAL. ";
        }

        // ********************************************************************************

        // **********************SI ES EL PRIMER PROCESO *********************************

        string cadenaAlmacen = null;
        if (esprimerproceso)
        {
            cadenaAlmacen += _dataManipulationService.GetObservacionesAlmacen(idPedido, true, false, yaprogramado);
            if (hojasYaTiradas > 0)
            {
                cadenaAlmacen += " (YP) ";
            }
            if (cogerde)
            {
                cadena += $"{Environment.NewLine}COGER DE ....";
            }
        }

        // ************SI ES EL PRIMER PROCESO Y ES DE ARDAGH REUS PONER COGER LA HOJALATA JUSTA 18/11/2015*********************************

        // hay que copiar las especificaciones de la hojalata que aparecen en la etiqueta exterior en la interior.
        if (esprimerproceso & pedidoProcesado.IdCliente == 73)
            cadena += $"{Environment.NewLine} COGER LAS HOJAS JUSTAS INDICADAS EN EL PEDIDO.";


        // ************10/02/2022 SI ES EL PRIMER PROCESO Y ES DE RAMONDÍN HAY QUE CAMBIAR LAS TABLAS *********************************

        // hay que copiar las especificaciones de la hojalata que aparecen en la etiqueta exterior en la interior.
        if (esprimerproceso & pedidoProcesado.IdCliente == 100)
            cadena += $"{Environment.NewLine} CAMBIAR LOS PALETS, USAR LOS INTERNOS DE 981*775.";

        //'******28/06/22 SI SON PEDIDOS DE LA MARCA ABBOTT ******
        if (esprimerproceso && pedidoProcesado.IdCliente == 84 && pedidoProcesado.Motivos.Contains("abbot") && codigoAplicacion == 620110000)
        {
            var hojas_maximas = (int)Math.Max(_dataManipulationService.DevuelveHojasMax(idPedido), _dataManipulationService.DevuelveHojasMin(idPedido) + 65);

            // SOLO ENTRA SI EL NÚMERO DE HOJAS devuelve_hojas_mn(IdPedido) + 65 ES MAYOR QUE devuelve_hojas_mx(IdPedido)
            if (hojas_maximas > _dataManipulationService.DevuelveHojasMax(idPedido))
            {
                cadena += $"{Environment.NewLine} PROCESAR COMO MÍNIMO: {hojas_maximas} HOJAS.";
            }
        }
        //26/03/2024 Cliente Frinsa, notas para planos de tapas TRR125L8-XX
        var listaPlanos1 = new List<string> { "TRR125L8-02", "TRR125L8-03", "TRR125L8-06", "TRR125L8-07" };
        var listaPlanos2 = new List<string> { "TRR125L8-04", "TRR125L8-05" };
        var listaPlanos3 = new List<string> { "TRR125L8-08", "TRR125L8-09" };
        if (pedidoProcesado.IdCliente == 6)
        {
            if (listaPlanos1.Contains(pedidoProcesado.Plano.ToUpperInvariant()))
            {
                cadena += $"{Environment.NewLine}TIRAR DE PUNTA & TACOS DEL PALLET DE PUNTA (PARALELOS AL 968)";
                if (codigoAplicacion.ToString().StartsWith("620"))
                {
                    cadena += $"{Environment.NewLine}IMPORTANTE!! VIGILAR TRANSVERSAL EN PINZA (QUE NO ENTRE EN EL TRABAJO)";
                }
            }
            else if (listaPlanos2.Contains(pedidoProcesado.Plano.ToUpperInvariant()))
            {
                cadena += $"{Environment.NewLine}TIRAR DE PUNTA & TACOS DEL PALLET DE PUNTA (PARALELOS AL 972)";
                if (codigoAplicacion.ToString().StartsWith("620"))
                {
                    cadena += $"{Environment.NewLine}IMPORTANTE!! VIGILAR TRANSVERSAL EN PINZA (QUE NO ENTRE EN EL TRABAJO)";
                }
            }
            else if (listaPlanos3.Contains(pedidoProcesado.Plano.ToUpperInvariant()))
            {
                cadena += $"{Environment.NewLine}TIRAR DE PUNTA & TACOS DEL PALLET DE PUNTA (PARALELOS AL 970)";
            }
        }
        //12/07/2024 Se unifica para Boue, Suiza y Alemania (12, 14, 18)
        if (pedidoProcesado.IdCliente is 12 or 14 or 18)
        {
            if (codigoAplicacion.ToString().StartsWith("6201"))
            {
                cadena += $"{Environment.NewLine}RESERVAS LONGITUDINALES NO PUEDEN SER MÁS ESTRECHAS DE 5,4mm.";
            }
            else
            {
                cadena += $"{Environment.NewLine}RESERVAS LONGITUDINALES NO PUEDEN SER MÁS ESTRECHAS DE 5mm.";
            }

            if (pedidoProcesado.IdCliente is 14 && pedidoProcesado.Obs1.Contains("NUEVO DISEÑO", StringComparison.OrdinalIgnoreCase))
            {
                cadena += $"{Environment.NewLine}NUEVO DISEÑO{Environment.NewLine}";
            }
        }

        //21/11/2024 Cambio para RAMONDIN
        if (pedidoProcesado.Plano.Contains("TAPON29-04", StringComparison.OrdinalIgnoreCase) && pedidoProcesado.IdCliente == 100)
        {
            cadena += $"{Environment.NewLine}TIRAR DE PUNTA";
        }

        //10/02/2025 CAMBIO PARA IBEREMBAL
        if (pedidoProcesado.Plano.Contains("064L5-01", StringComparison.OrdinalIgnoreCase) && pedidoProcesado.IdCliente == 44)
        {
            cadena += $"{Environment.NewLine}VIGILAR POSICIÓN RESERVAS";
        }

        //'FIN 28/06/22*** 'reservas estrechas con plano de Reus*************************************************
        if (pedidoProcesado.IdCliente == 73 && pedidoProcesado.Plano == "065L5-02" && caraAAplicar.StartsWith("e"))
        {
            cadena += $"{Environment.NewLine}USAR RODILLO CON RESERVAS ESTRECHAS.";
        }

        //26/03/2024 - Comentario para Trivium Reus y Palabra Nata en motivo, es cara E1 y codApli 620XXXX
        //16/07/2024 - Añadido tambien por Javi, para el cliente 26, corregido por Esteban
        //19/07/2024 - Corregido por César por nuevas específicaciones, para el cliente 26, no hay que evaluar que el motivo contenga "NATA"
        if (datosOrdenProcesos.Cara == "e1" &&
           ((pedidoProcesado.IdCliente == 73 && datosOrdenProcesos.CodApli.ToString().StartsWith("620") && pedidoProcesado.Motivos.ToUpperInvariant().Contains("NATA")) ||
            (pedidoProcesado.IdCliente == 26 && datosOrdenProcesos.CodApli.ToString().StartsWith("6201"))))
        {
            cadena += $"{Environment.NewLine}OJO NO MANCHAR INTERIOR.";
        }
        //08/01/2025 - Indicado por Carlos
        if (pedidoProcesado.IdCliente == 26 && pedidoProcesado.Plano == "0286L1-02")
        {
            cadena += $"{Environment.NewLine}DE PUNTA.";
        }

        // ****** 02/05/2020: CUANDO ES UNA MÁQUINA TIPO TANDEM, NO SE INDICA EN EL PRIMER PROCESO, SE INDICA EN EL SEGUNDO.
        cadena += _dataManipulationService.ObtenerMarcadoTamponExternalInternal(idPedido, tp.Idlinea.Value, esprimerproceso, ordenproceso);

        var siguienteProcesoTexto = _dataManipulationService.DevuelveDatosAplicacion(datosOrdenProcesos.CodApliPosterior, "NombreApliProgramacion");
        string cadenaprocesoposterior;
        switch (esultimoproceso)
        {
            // Adaptación Funcion devuelve_cadena_proceso_posterior y consideramos que SIEMPRE los valores van a ser ep, e1...X e i1....X
            case false:
                var txtApliPosterior = "LUEGO " + siguienteProcesoTexto;
                var caraPosterior = datosOrdenProcesos.CaraPosterior;

                txtApliPosterior += caraPosterior == "ep"
                    ? " POR CARA EXTERIOR"
                    : " POR CARA " + (caraPosterior[0] == 'i' ? "INTERIOR" : "EXTERIOR");

                cadenaprocesoposterior = txtApliPosterior + ".";
                break;
            // ********************************************************************************
            // **********************SI ES EL ULTIMO PROCESO *********************************
            case true:
                cadenaprocesoposterior = _dataManipulationService.DevuelveObservacionesFlejado(idPedido);
                break;
        }

        // si es el último proceso y en observaciones de flejado no aparece "LUEGO LITOGRAFÍA"
        // marcar para flejar y forma de flejadoFLEJAR EXTERIOR ARRIBA

        if (esultimoproceso)
        {
            cadenaprocesoposterior = pedidoProcesado.Obsflejado.Contains("LUEGO")
                ? cadenaprocesoposterior += (string.IsNullOrEmpty(cadenaprocesoposterior) ? "" : Environment.NewLine) + "PEDIDO DE BASES, LUEGO LITO."
                : cadenaprocesoposterior += Environment.NewLine + pedidoProcesado.Obsflejado;
        }

        // ********************************************************************************
        //tp.Idproducto == Form_Frm_programacion_ppal.txt_producto2?????? NO SE YO
        //vigilar codigoAplicacion tambien
        if (Strings.Left(codigoAplicacion.ToString(), 4) == "4204" &&
            pedidoProcesado.Motivos.Contains("CASSEGRAIN", StringComparison.OrdinalIgnoreCase))
        {
            cadena += _dataManipulationService.AñadeObservacionesCodigosYBarnices(Strings.Left(codigoAplicacion.ToString(), 4),
                tp.Idproducto.ToString().Trim(),
                pedidoProcesado.IdCliente.ToString().Trim());
        }
        else if (Strings.Left(codigoAplicacion.ToString(), 4) == "4204" &&
                 !pedidoProcesado.Motivos.Contains("CASSEGRAIN", StringComparison.OrdinalIgnoreCase))
        {
            cadena += string.Empty;
        }
        else
        {
            cadena += _dataManipulationService.AñadeObservacionesCodigosYBarnices(Strings.Left(codigoAplicacion.ToString(), 4),
                tp.Idproducto.ToString().Trim(),
                pedidoProcesado.IdCliente.ToString().Trim());
        }

        // 10/11/2022 forzamos la entrada para códigos de aplicación completos, es decir con 951501501, nos devuelve un comentario en el caso que exista en la tabla comentarios
        if (codigoAplicacion > 950500000 & codigoAplicacion < 962100000)
            cadena += _dataManipulationService.AñadeObservacionesCodigosYBarnices(codigoAplicacion.ToString(), 0.ToString(), 0.ToString());


        // 27/08/2019 CLIENTES PARA LOS QUE NO SE PUEDEN JUNTAR PAQUETES.
        // ***************************************


        if (pedidoProcesado.IdCliente == 6)
            cadena = cadena.TrimEnd('\r', '\n') + $"{Environment.NewLine}NO JUNTAR PAQUETES.";

        // 10-03-2025 - NUEVO COMENTARIO PARA 00032 SOLOCAP-MAB POR RECLAMACIÓN 2025-RC-081
        if (pedidoProcesado.IdCliente == 32 && (codigoAplicacion == 940906001 || codigoAplicacion == 975304002))
            cadena += $"{Environment.NewLine}ATENCIÓN ESPECIAL: RECLAMACIÓN DEL CLIENTE POR HOJAS MAL POSICIONADAS.";

        res.Observaciones = cadena;
        res.Obspaseposterior = cadenaprocesoposterior;
        res.ObsAlmacen = res.Observaciones.Contains("$$") ? string.Empty : cadenaAlmacen;

        var cadenaObsCalidad =
            await _litalsaDataWarehouseContext.NotasProgramacion.FirstOrDefaultAsync(
                o => o.PedidoId == pedidoProcesado.IdPedido && o.AplicacionId == codigoAplicacion, ca);

        res.ObsCalidad = cadenaObsCalidad == null
            ? string.Empty
            : $"{codigoAplicacion} // {cadenaObsCalidad?.Texto ?? string.Empty}";

        return res;
    } // FIN COMPLETA OBSERVACIONES
}