﻿@using ProgramadorGeneralBLZ.Shared
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.WebAssembly.Authentication
@using Microsoft.AspNetCore.Components.WebAssembly.Hosting
@using ProgramadorGeneralBLZ.Shared.DTO

@inject AuthenticationStateProvider GetAuthenticationStateAsync
@inject NavigationManager Navigation
@inject IWebAssemblyHostEnvironment HostEnv
@inject FiltroService FiltroService

<div class="sidebar @StateCssClass">
    <nav class="flex-column">
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                <span class="oi oi-home" aria-hidden="true"></span> <span class="@StateCssClass">Home</span>
            </NavLink>
        </div>
        <DxMenu Orientation="Orientation.Horizontal"
                ItemsPosition="ItemPosition.Center"
                ItemsStretched="false" CssClass="transparente"
                DropDownActionMode="MenuDropDownActionMode.Click"
                CollapseItemToIconMode="MenuCollapseItemToIconMode.Sequentially"
                CollapseItemsToHamburgerMenu="false">
            <Items>
                <AuthorizeView>
                    <NotAuthorized Context="contextNotAuth">
                        @*<DxButton RenderStyle="ButtonRenderStyle.Info"
                        Text="Acceder" SizeMode="SizeMode.Large"
                        IconCssClass="oi oi-account-login iconInButton"
                        CssClass="mx-3"
                        Click="@NavigateToLogin" />*@
                        <div class="nav-item px-3 transparente">
                            <NavLink class="nav-link transparente">
                                <span class="oi oi-account-login" @onclick="NavigateToLogin" aria-hidden="true"></span> <span class="@StateCssClass">Login</span>
                            </NavLink>
                        </div>
                    </NotAuthorized>
                </AuthorizeView>
            </Items>
        </DxMenu>
        <AuthorizeView Context="contextAuth">
	        @*Hay que añadir aquí los roles de las máquinas para que se vean.*@
	        <AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}, {Roles.Encargado}, {Roles.JefeTurno}, {Roles.Rodillos}, {Roles.B3}, {Roles.B5}, {Roles.M4}")">
		        <div class="nav-item px-3">
			        <NavLink class="nav-link" href="VerProgramacion">
				        <span class="oi oi-eye" aria-hidden="true"></span> <span class="@StateCssClass">Ver Programacion</span>
			        </NavLink>
		        </div>
	        </AuthorizeView>
            <AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}")">
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="Actualizaciones">
                        <span class="oi oi-cog" aria-hidden="true" title="Procesos de Actualización"></span> <span class="@StateCssClass">Procesos de Actualización</span>
                    </NavLink>
                </div>
            </AuthorizeView>
            <AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}, {Roles.Consulta}")">
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="ProgramacionPedidos">
                        <span class="oi oi-file" aria-hidden="true" title="Pedidos"></span> <span class="@StateCssClass">Pedidos</span>
                    </NavLink>
                </div>
            </AuthorizeView>
            <AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}")">
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="ProgramacionBarnizado">
                        <span class="oi oi-droplet" aria-hidden="true" title="Prog. Barnizado"></span> <span class="@StateCssClass">Prog. Barnizado</span>
                    </NavLink>
                </div>
            </AuthorizeView>
            <AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}")">
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="ProgramacionLitografia">
                        <span class="oi oi-image" aria-hidden="true" title="Prog. Litografía"></span> <span class="@StateCssClass">Prog. Litografía</span>
                    </NavLink>
                </div>
            </AuthorizeView>
            <AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}")">
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="ConsultaFechas">
                        <span class="oi oi-calendar" aria-hidden="true" title="Consulta Fechas"></span> <span class="@StateCssClass">Consulta Fechas</span>
                    </NavLink>
                </div>
            </AuthorizeView>
            <AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}")">
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="Entregas">
                        <span class="oi oi-inbox" aria-hidden="true" title="Entregas"></span> <span class="@StateCssClass">Entregas</span>
                    </NavLink>
                </div>
            </AuthorizeView>
            <AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}")">
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="GestionTablas">
                        <span class="oi oi-spreadsheet" aria-hidden="true" title="Gestión Tablas"></span> <span class="@StateCssClass">Gestión Tablas</span>
                    </NavLink>
                </div>
            </AuthorizeView>
            <AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}, {Roles.Consulta}")">
	            <div class="nav-item px-3">
		            <NavLink class="nav-link" href="PedidosFiltrados" @onclick="@(Filtro)">
			            @* <span class="oi oi-spreadsheet" aria-hidden="true" title="Filtro General"></span> *@
			            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-funnel-fill" viewBox="0 0 16 16">
				            <path d="M1.5 1.5A.5.5 0 0 1 2 1h12a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.128.334L10 8.692V13.5a.5.5 0 0 1-.342.474l-3 1A.5.5 0 0 1 6 14.5V8.692L1.628 3.834A.5.5 0 0 1 1.5 3.5v-2z" />
			            </svg>
			            <span class="@StateCssClass">Filtro General</span>
		            </NavLink>
	            </div>
            </AuthorizeView>
            <AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}, {Roles.Consulta}")">
	            <div class="nav-item px-3">
		            <NavLink class="nav-link" href="Ayuda">
			            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-question-square-fill" viewBox="0 0 16 16">
				            <path d="M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2zm3.496 6.033a.237.237 0 0 1-.24-.247C5.35 4.091 6.737 3.5 8.005 3.5c1.396 0 2.672.73 2.672 2.24 0 1.08-.635 1.594-1.244 2.057-.737.559-1.01.768-1.01 1.486v.105a.25.25 0 0 1-.25.25h-.81a.25.25 0 0 1-.25-.246l-.004-.217c-.038-.927.495-1.498 1.168-1.987.59-.444.965-.736.965-1.371 0-.825-.628-1.168-1.314-1.168-.803 0-1.253.478-1.342 1.134-.018.137-.128.25-.266.25h-.825zm2.325 6.443c-.584 0-1.009-.394-1.009-.927 0-.552.425-.94 1.01-.94.609 0 1.028.388 1.028.94 0 .533-.42.927-1.029.927"/>
			            </svg>
			            <span aria-hidden="true" title="Ayuda"></span> <span class="@StateCssClass">Ayuda</span>
		            </NavLink>
	            </div>
            </AuthorizeView>
            <AuthorizeView>
                <div class="nav-item px-3">
                    <NavLink class="nav-link">
                        <span class="oi oi-account-logout" @onclick="(BeginSignOut)" aria-hidden="true" title="Logout"></span> <span class="@StateCssClass">Logout</span>
                    </NavLink>
                </div>
            </AuthorizeView>
            @*            <div class="nav-item px-3">
            <NavLink class="nav-link" href="Counter">
            <span class="oi oi-image" aria-hidden="true"></span> <span class="@StateCssClass">Counter</span>
            </NavLink>
            </div>
            <div class="nav-item px-3">
            <NavLink class="nav-link" href="FetchData">
            <span class="oi oi-image" aria-hidden="true"></span> <span class="@StateCssClass">Fetch</span>
            </NavLink>
            </div>*@

        </AuthorizeView>

    </nav>
</div>

@code {
    [Parameter] public string? StateCssClass { get; set; }

    private bool collapseNavMenu = true;

    private string? NavMenuCssClass => collapseNavMenu ? "collapse" : null;

    private void ToggleNavMenu()
    {
        collapseNavMenu = !collapseNavMenu;
    }
    private async Task BeginSignOut()
    {
        Navigation.NavigateToLogout("authentication/logout");
    }

    private void NavigateToLogin()
    {
        Navigation.NavigateTo("authentication/login");
    }

    async Task Filtro()
    {
        var filtro = new FiltroDTO
        {
            General = true
        };
        await FiltroService.SetFiltroAsync(filtro);
    }
}
