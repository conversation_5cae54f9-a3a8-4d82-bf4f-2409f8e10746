﻿@using Microsoft.AspNetCore.Components.WebAssembly.Authentication
@using ProgramadorGeneralBLZ.Client.Pages.Components
@using ProgramadorGeneralBLZ.Shared
@using ProgramadorGeneralBLZ.Shared.DTO

@inherits LayoutComponentBase

@inject IHttpClientFactory HttpClientFactory
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@inject FiltroService FiltroService

<DxLayoutBreakpoint DeviceSize="DeviceSize.XSmall" IsActive="IsXSmallScreen" IsActiveChanged="IsActiveChanged" />
<div class="page @(esDesarrollo? "fondoDesarrollo": "" )">
    <BlazoredToasts MaxToastCount="3" RemoveToastsOnNavigation="true"  ShowProgressBar="true" Timeout="5"/>
    <DxLoadingPanel @bind-Visible="_inactivo"
                    IsContentBlocked="true"
                    CloseOnClick="true"
                    IndicatorAnimationType="WaitIndicatorAnimationType.Spin"
                    Text="La aplicación está inactiva, por favor recarga la página."
                    ApplyBackgroundShading="true"
                    IndicatorAreaVisible="true"
                    ZIndex="5000">
	    <div class="card border-0 vh-100 layout-item">
		    <SpinnerV2></SpinnerV2>
		    <DxDrawer @bind-IsOpen="IsOpen"
		              Mode="DrawerMode.Shrink"
		              PanelWidth="260px"
		              MiniModeEnabled="true"
		              MiniPanelWidth="65px">
			    <BodyTemplate>
				    <div class="flex-column h-100 w-100 overflow-hidden sidebar">
					    <div class="nav-item">
						    <div class="contenedor-icono-hamburger px-3 d-flex align-items-center justify-content-center">
							    <button class="navbar-toggler" @onclick="ToggleDrawer">
								    <span class="navbar-toggler-icon"></span>
							    </button>
						    </div>
					    </div>
					    <div class="nav-item">
						    <NavLink class="nav-link mx-2" href="" Match="NavLinkMatch.All" title="Home">
							    <div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
								    <span class="bi bi-house icono-sidebar"></span>
							    </div>
							    <span class="ms-3 text-nowrap">Home</span>
						    </NavLink>
					    </div>
					    <AuthorizeView>
						    <NotAuthorized Context="contextNotAuth">
							    <div class="nav-item" @onclick="NavigateToLogin">
								    <NavLink class="nav-link mx-2"
								             title="Login"
								             href="login"
								             @onclick:preventDefault="true">
									    <div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
										    <span class="bi bi-box-arrow-in-right icono-sidebar"></span>
									    </div>
									    <span class="ms-3 text-nowrap">Login</span>
								    </NavLink>
							    </div>
						    </NotAuthorized>
					    </AuthorizeView>
					    <AuthorizeView Context="contextAuth">
						    @*Hay que añadir aquí los roles de las máquinas para que se vean.*@
						    <AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}, {Roles.Encargado}, {Roles.JefeTurno}, {Roles.Rodillos}, {Roles.B3}, {Roles.B5}, {Roles.M4}")">
							    <div class="nav-item">
								    <NavLink class="nav-link mx-2" href="VerProgramacion" title="Ver Programación">
									    <div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
										    <span class="bi bi-eye-fill icono-sidebar"></span>
									    </div>
									    <span class="ms-3 text-nowrap">Ver Programación</span>
								    </NavLink>
							    </div>
						    </AuthorizeView>
						    <AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}")">
							    <div class="nav-item">
								    <NavLink class="nav-link mx-2" href="Actualizaciones" title="Procesos de Actualización">
									    <div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
										    <span class="bi bi-gear-fill icono-sidebar"></span>
									    </div>
									    <span class="ms-3 text-nowrap">Procesos de Actualización</span>
								    </NavLink>
							    </div>
						    </AuthorizeView>
						    <AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}, {Roles.Consulta}")">
							    <div class="nav-item">
								    <NavLink class="nav-link mx-2" href="ProgramacionPedidos" title="Pedidos">
									    <div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
										    <span class="bi bi-file-earmark-fill icono-sidebar"></span>
									    </div>
									    <span class="ms-3 text-nowrap">Pedidos</span>
								    </NavLink>
							    </div>
						    </AuthorizeView>
						    <AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}")">
							    <div class="nav-item">
								    <NavLink class="nav-link mx-2" href="ProgramacionBarnizado" title="Prog. Barnizado">
									    <div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
										    <span class="bi bi-droplet-fill icono-sidebar"></span>
									    </div>
									    <span class="ms-3 text-nowrap">Prog. Barnizado</span>
								    </NavLink>
							    </div>
						    </AuthorizeView>
						    <AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}")">
							    <div class="nav-item">
								    <NavLink class="nav-link mx-2" href="ProgramacionLitografia" title="Prog. Litografía">
									    <div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
										    <span class="bi bi-image icono-sidebar"></span>
									    </div>
									    <span class="ms-3 text-nowrap">Prog. Litografía</span>
								    </NavLink>
							    </div>
						    </AuthorizeView>
						    <AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}")">
							    <div class="nav-item">
								    <NavLink class="nav-link mx-2" href="ConsultaFechas" title="Consulta Fechas">
									    <div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
										    <span class="bi bi-calendar2-week-fill icono-sidebar"></span>
									    </div>
									    <span class="ms-3 text-nowrap">Consulta Fechas</span>
								    </NavLink>
							    </div>
						    </AuthorizeView>
						    <AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}")">
							    <div class="nav-item">
								    <NavLink class="nav-link mx-2" href="Entregas" title="Entregas">
									    <div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
										    <span class="bi bi-inbox-fill icono-sidebar"></span>
									    </div>
									    <span class="ms-3 text-nowrap">Entregas</span>
								    </NavLink>
							    </div>
						    </AuthorizeView>
						    <AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}")">
							    <div class="nav-item">
								    <NavLink class="nav-link mx-2" href="GestionTablas" title="Gestión Tablas">
									    <div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
										    <span class="bi bi-table icono-sidebar"></span>
									    </div>
									    <span class="ms-3 text-nowrap">Gestión Tablas</span>
								    </NavLink>
							    </div>
						    </AuthorizeView>
						    <AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}, {Roles.Consulta}")">
							    <div class="nav-item">
								    <NavLink class="nav-link mx-2" href="PedidosFiltrados" title="Filtro General" @onclick="@(Filtro)">
									    <div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
										    <span class="bi bi-funnel-fill icono-sidebar"></span>
									    </div>
									    <span class="ms-3 text-nowrap">Filtro General</span>
								    </NavLink>
							    </div>
						    </AuthorizeView>
						    <AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}, {Roles.Consulta}")">
							    <div class="nav-item">
								    <NavLink class="nav-link mx-2" href="Ayuda" title="Ayuda">
									    <div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
										    <span class="bi bi-question-square-fill icono-sidebar"></span>
									    </div>
									    <span class="ms-3 text-nowrap">Ayuda</span>
								    </NavLink>
							    </div>
						    </AuthorizeView>
						    <AuthorizeView>
							    <div class="nav-item" @onclick="(BeginSignOut)">
								    <NavLink class="nav-link mx-2"
								             title="Logout"
								             href="logout"
								             @onclick:preventDefault="true">
									    <div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
										    <span class="bi bi-box-arrow-left icono-sidebar"></span>
									    </div>
									    <span class="ms-3 text-nowrap">Logout</span>
								    </NavLink>
							    </div>
						    </AuthorizeView>
					    </AuthorizeView>
				    </div>
			    </BodyTemplate>
			    <TargetContent>
					<div id="div-body" class="w-100 h-100 m-0 px-1 py-0 overflow-auto d-flex flex-column">
					    @Body
				    </div>
			    </TargetContent>
		    </DxDrawer>
	    </div>
    </DxLoadingPanel>
</div>

@code {
	// https://docs.devexpress.com/Blazor/DevExpress.Blazor.DxMenu
	// https://demos.devexpress.com/blazor/Drawer#Position
	private bool IsXSmallScreen { get; set; }
	private bool? _isOpen = false; // por defecto cerrado en vista completa
	private bool? _isOpenBigScreen = true; // estado guardado para pantallas grandes

    private bool _inactivo = false;
    private bool esDesarrollo;

    private bool IsOpen
    {
	    get => _isOpen ?? !IsXSmallScreen;
	    set => _isOpen = value;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            var dotNetReference = DotNetObjectReference.Create(this);
            await JSRuntime.InvokeVoidAsync("startInactivityTimer", dotNetReference);
        }

    }
    protected override async Task OnInitializedAsync()
    {
        var client = HttpClientFactory.CreateClient("DatabaseAPI");
        esDesarrollo = Convert.ToBoolean(await client.GetStringAsync("ConsultaDB/current-database"));

    }

    [JSInvokable]
    public void SetInactivo(bool valor)
    {
        _inactivo = valor;
        InvokeAsync(StateHasChanged); // Para asegurar que la UI se actualice
    }

    private void IsActiveChanged(bool isXSmall)
    {
	    var previousIsXSmall = IsXSmallScreen;
	    IsXSmallScreen = isXSmall;

	    if (isXSmall)
	    {
		    // guardamos el estado actual para cuando vuelva a pantalla grande
		    if (!previousIsXSmall)
			    _isOpenBigScreen = _isOpen;
		    // en pantalla pequeña inicialmente cerrado
		    _isOpen = false;
	    }
	    else
	    {
		    if (previousIsXSmall && _isOpen == true) // si estaba abierto en pantalla pequeña, lo abrimos en grande
			    _isOpen = true;
		    else // si no, restauramos el estado guardado previamente
			    _isOpen = _isOpenBigScreen;
	    }
    }

    private void ToggleDrawer()
    {
	    IsOpen = !IsOpen;

	    // guardamos el estado para pantallas grandes
	    if (!IsXSmallScreen)
		    _isOpenBigScreen = IsOpen;
    }

    private async Task Filtro()
    {
	    var filtro = new FiltroDTO
	    {
		    General = true
	    };
	    await FiltroService.SetFiltroAsync(filtro);
    }

    private async Task BeginSignOut()
    {
	    Navigation.NavigateToLogout("authentication/logout");
    }

    private void NavigateToLogin()
    {
	    Navigation.NavigateTo("authentication/login");
    }
}