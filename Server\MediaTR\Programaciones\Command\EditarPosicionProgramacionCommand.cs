using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Programaciones.Command;

using Nelibur.ObjectMapper;

public class EditarPosicionProgramacionCommand : IRequest<SingleResult<int>>
{

    public EditarPosicionProgramacionCommand(int posicion, TablaProgramacionDTO tprog)
    {
        Posicion = posicion;
        TablaProg = tprog;
    }

    public TablaProgramacionDTO TablaProg { get; set; }
    public int Posicion { get; set; }
}

public class EditarPosicionProgramacionCommandHandler : IRequestHandler<EditarPosicionProgramacionCommand, SingleResult<int>>
{
    private readonly ProgramadorLitalsaContext _contextProg;
    private readonly IDataManipulationService _dataManipulationService;
    public EditarPosicionProgramacionCommandHandler(ProgramadorLitalsaContext contextProg, IDataManipulationService dataManipulationService)
    {
        _contextProg = contextProg;
        _dataManipulationService = dataManipulationService;
    }

    public async Task<SingleResult<int>> Handle(EditarPosicionProgramacionCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int> { Errors = new List<string>(), Data = 0 };
        try
        {
            var progModificada = request.TablaProg;
            var prog =
                _contextProg.TablaProgramacion.FirstOrDefault(o =>
                    //o.Posicion == progModificada.Posicion && 
                    o.Idpedido == progModificada.Idpedido &&
                    o.Idprogramacion == progModificada.Idprogramacion);
            //Para segurar que siempre aparece al final.
            if (prog.Idlinea != progModificada.Idlinea)
            {
                progModificada.Posicion += 9000000;
            }

            //Si se ha modificado el barniz/cod apli, se marca el campo Observaciones con "*" al principio
            // Comprobar si 'Idaplicacion' o 'Idproducto' ha cambiado
            if (prog.Idaplicacion != progModificada.Idaplicacion || prog.Idproducto != progModificada.Idproducto)
            {
                progModificada.Observaciones = $"*{progModificada.Observaciones}";

                // Si 'Idaplicacion' ha cambiado, obtener el nuevo 'Codapli' y el nuevo 'Producto'
                int? barnizAConsultar = null;
                if (prog.Idaplicacion != progModificada.Idaplicacion)
                {
                    barnizAConsultar = _contextProg.Codapli.FirstOrDefault(o => o.Codbaz == progModificada.Idaplicacion.Value).Codbarniz;
                }
                // Si 'Idproducto' ha cambiado, obtener el nuevo 'Producto'
                else if (prog.Idproducto != progModificada.Idproducto)
                {
                    barnizAConsultar = progModificada.Idproducto;

                }
                var nuevoProducto = _contextProg.TablaProductos.FirstOrDefault(o => o.Idproducto == barnizAConsultar.Value);
                progModificada.Producto = $"{nuevoProducto?.Denominacion ?? string.Empty}";
                // Actualizar 'TemperaturaSecado' y 'VelocidadMaxima'

                // CÓDIGO VIEJO - COMENTADO 21/07/2025
                //progModificada.TemperaturaSecado = prog.Idaplicacion is 999 or 998 or 997 or 996
                //    ? (int?)await _dataManipulationService.GetTemperaturaSecado_LITO(progModificada.Idproducto, progModificada.Idlinea.Value, cancellationToken)
                //    : (int?)await _dataManipulationService.GetTemperaturaSecadoBarnizado(progModificada.Idproducto, progModificada.Idlinea.Value, cancellationToken);

                //progModificada.VelocidadMaxima = (int?)await _dataManipulationService.GetVelocidadMaxima(progModificada.Idproducto, progModificada.Idlinea.Value, cancellationToken);

                // NUEVO CÓDIGO - 21/07/2025 - Integración de lógica de ProgramarPedidosBarnizadoCommand
                //Obtener información de la máquina para determinar PosicionTandem
                var maquina = await _contextProg.Maquinas.FirstOrDefaultAsync(m => m.Idmaquina == progModificada.Idlinea.Value, cancellationToken);

                if (maquina != null)
                {
                    // Solo recalcular si la temperatura actual es 0 o null
                    if (progModificada.TemperaturaSecado is 0 or null)
                    {
                        switch (maquina.PosicionTandem)
                        {
                            case 1:
                                // Para asegurar que encontramos la velocidad de la maquina 1 del tandem, como ahora va por la tabla de velocidades
                                // hay que simular que somos la posición 2 del tandem y obtener la velocidad de su registro que dice que es para la maquina 1 del tandem
                                if (_contextProg.VelocidadesMaquina.Any(o => progModificada.Idaplicacion.ToString().StartsWith(o.CodApliMq1Consulta.ToString())))
                                {
                                    var progLineaSig = await _contextProg.TablaProgramacion
                                        .Where(o => o.Idpedido == progModificada.Idpedido && o.Idlinea == maquina.Idmaquina + 1)
                                        .OrderByDescending(o => o.Idprogramacion)
                                        .FirstOrDefaultAsync(cancellationToken);

                                    if (progLineaSig != null && progLineaSig.Idproducto == 110146)
                                    {
                                        var datosVelMaq = await _dataManipulationService.GetTemperaturaSecadoV2(110146, progModificada.Idpedido, maquina.Idmaquina + 1, cancellationToken);
                                        if (datosVelMaq?.VelTiradaMq1 != null)
                                        {
                                            progModificada.VelocidadMaxima = (int)datosVelMaq.VelTiradaMq1;
                                        }
                                    }
                                }
                                // Usar método con comparación de temperaturas
                                var temperaturaSecado1 = prog.Idaplicacion is 999 or 998 or 997 or 996
                                    ? (double?)await _dataManipulationService.GetTemperaturaSecado_LITO(progModificada.Idproducto, progModificada.Idlinea.Value, cancellationToken)
                                    : await _dataManipulationService.GetTemperaturaSecadoBarnizado(progModificada.Idproducto, progModificada.Idlinea.Value, cancellationToken);

                                var temperaturaAux1 = Convert.ToInt32(progModificada.Idaplicacion.Value.ToString().Substring(4, 3));
                                progModificada.TemperaturaSecado = (int?)Math.Max(temperaturaSecado1 ?? 0, temperaturaAux1);

                                if (progModificada.VelocidadMaxima == 0)
                                    progModificada.VelocidadMaxima = (int?)await _dataManipulationService.GetVelocidadMaxima(progModificada.Idproducto, maquina.Idmaquina, cancellationToken);
                                break;

                            case 2:
                                if (progModificada.Idproducto == 110146)
                                {
                                    var datosVelMaq = await _dataManipulationService.GetTemperaturaSecadoV2(progModificada.Idproducto, progModificada.Idpedido, maquina.Idmaquina, cancellationToken);
                                    if (datosVelMaq != null)
                                    {
                                        progModificada.TemperaturaSecado = (int?)datosVelMaq.TempTiradaMq2;
                                        progModificada.VelocidadMaxima = datosVelMaq.VelTiradaMq2;
                                    }
                                }

                                if (progModificada.TemperaturaSecado == 0)
                                {
                                    var temperaturaSecado = await _dataManipulationService.GetTemperaturaSecado(progModificada.Idproducto, progModificada.Idpedido, 
                                        TinyMapper.Map<MaquinaDTO>(maquina),
                                        cancellationToken);
                                    var temperaturaAux = Convert.ToInt32(progModificada.Idaplicacion.Value.ToString().Substring(4, 3));
                                    progModificada.TemperaturaSecado = (int?)Math.Max(temperaturaSecado, temperaturaAux);
                                }

                                if (progModificada.VelocidadMaxima == 0)
                                    progModificada.VelocidadMaxima = (int?)await _dataManipulationService.GetVelocidadMaxima(progModificada.Idproducto, maquina.Idmaquina, cancellationToken);
                                break;

                            default: // case 0 y otros
                                // Para máquinas NO en tándem
                                var temperaturaSecado0 = prog.Idaplicacion is 999 or 998 or 997 or 996
                                    ? (double?)await _dataManipulationService.GetTemperaturaSecado_LITO(progModificada.Idproducto, progModificada.Idlinea.Value, cancellationToken)
                                    : await _dataManipulationService.GetTemperaturaSecadoBarnizado(progModificada.Idproducto, progModificada.Idlinea.Value, cancellationToken);

                                progModificada.TemperaturaSecado = (int?)temperaturaSecado0;
                                break;
                        }
                    }

                    // Solo recalcular velocidad si es 0 o null
                    if (progModificada.VelocidadMaxima == 0 || progModificada.VelocidadMaxima == null)
                    {
                        progModificada.VelocidadMaxima = (int?)await _dataManipulationService.GetVelocidadMaxima(progModificada.Idproducto, progModificada.Idlinea.Value, cancellationToken);
                    }
                }
            }

            if (progModificada.Idproducto != null)
            {
                progModificada.Producto = progModificada.PesoMin.HasValue && progModificada.Peso.HasValue
                    ? $"{_contextProg.TablaProductos.FirstOrDefault(o => o.Idproducto == progModificada.Idproducto).Denominacion} Peso: {progModificada.PesoMin} - {progModificada.Peso} g/m2"
                    : $"{_contextProg.TablaProductos.FirstOrDefault(o => o.Idproducto == progModificada.Idproducto).Denominacion}";
            }

            _contextProg.Entry(prog).CurrentValues.SetValues(progModificada);
            var dbResult = await _contextProg.SaveChangesAsync(cancellationToken);
            result.Data = dbResult;
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: EditarPosicionProgramacionCommand - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }
        return result;
    }
}