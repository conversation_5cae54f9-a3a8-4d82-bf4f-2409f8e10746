using MediatR;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Programaciones.Command;

public class EditarPosicionProgramacionCommand : IRequest<SingleResult<int>>
{

    public EditarPosicionProgramacionCommand(int posicion, TablaProgramacionDTO tprog)
    {
        Posicion = posicion;
        TablaProg = tprog;
    }

    public TablaProgramacionDTO TablaProg { get; set; }
    public int Posicion { get; set; }
}

public class EditarPosicionProgramacionCommandHandler : IRequestHandler<EditarPosicionProgramacionCommand, SingleResult<int>>
{
    private readonly ProgramadorLitalsaContext _contextProg;
    private readonly IDataManipulationService _dataManipulationService;
    public EditarPosicionProgramacionCommandHandler(ProgramadorLitalsaContext contextProg, IDataManipulationService dataManipulationService)
    {
        _contextProg = contextProg;
        _dataManipulationService = dataManipulationService;
    }

    public async Task<SingleResult<int>> Handle(EditarPosicionProgramacionCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int> { Errors = new List<string>(), Data = 0 };
        try
        {
            var progModificada = request.TablaProg;
            var prog =
                _contextProg.TablaProgramacion.FirstOrDefault(o =>
                    //o.Posicion == progModificada.Posicion && 
                    o.Idpedido == progModificada.Idpedido &&
                    o.Idprogramacion == progModificada.Idprogramacion);
            //Para segurar que siempre aparece al final.
            if (prog.Idlinea != progModificada.Idlinea)
            {
                progModificada.Posicion += 9000000;
            }

            //Si se ha modificado el barniz/cod apli, se marca el campo Observaciones con "*" al principio
            // Comprobar si 'Idaplicacion' o 'Idproducto' ha cambiado
            if (prog.Idaplicacion != progModificada.Idaplicacion || prog.Idproducto != progModificada.Idproducto)
            {
                progModificada.Observaciones = $"*{progModificada.Observaciones}";

                // Si 'Idaplicacion' ha cambiado, obtener el nuevo 'Codapli' y el nuevo 'Producto'
                int? barnizAConsultar = null;
                if (prog.Idaplicacion != progModificada.Idaplicacion)
                {
                    barnizAConsultar = _contextProg.Codapli.FirstOrDefault(o => o.Codbaz == progModificada.Idaplicacion.Value).Codbarniz;
                }
                // Si 'Idproducto' ha cambiado, obtener el nuevo 'Producto'
                else if (prog.Idproducto != progModificada.Idproducto)
                {
                    barnizAConsultar = progModificada.Idproducto;

                }
                var nuevoProducto = _contextProg.TablaProductos.FirstOrDefault(o => o.Idproducto == barnizAConsultar.Value);
                progModificada.Producto = $"{nuevoProducto?.Denominacion ?? string.Empty}";
                // Actualizar 'TemperaturaSecado' y 'VelocidadMaxima'

                // COMENTADO 22/07/25
                //progModificada.TemperaturaSecado = prog.Idaplicacion is 999 or 998 or 997 or 996
                //    ? (int?)await _dataManipulationService.GetTemperaturaSecado_LITO(progModificada.Idproducto, progModificada.Idlinea.Value, cancellationToken)
                //    : (int?)await _dataManipulationService.GetTemperaturaSecadoBarnizado(progModificada.Idproducto, progModificada.Idlinea.Value, cancellationToken);

                // NUEVO 22/07/25
                if (progModificada.Idaplicacion is 999 or 998 or 997 or 996)
                {
                    progModificada.TemperaturaSecado =
                        (int?)await _dataManipulationService.GetTemperaturaSecado_LITO(progModificada.Idproducto,
                            progModificada.Idlinea.Value, cancellationToken);
                }
                else
                {
                    // 22/07/25 - SI SON PASES DE SECADO, LA TEMPERATURA SE OBTIENE DEL PROPIO CÓDIGO DE APLICACIÓN
                    var temperaturaAux =(int?)
                        (progModificada.Idaplicacion.Value > 100012000 && progModificada.Idaplicacion.Value < 100020500
                        ? Convert.ToInt32(progModificada.Idaplicacion.Value.ToString().Substring(4, 3))
                        : 0);
                    var temperaturaSecado =
                        (int?)await _dataManipulationService.GetTemperaturaSecadoBarnizado(progModificada.Idproducto,
                            progModificada.Idlinea.Value, cancellationToken);
                    progModificada.TemperaturaSecado = Math.Max(temperaturaSecado, temperaturaAux);
                }
                progModificada.VelocidadMaxima = (int?)await _dataManipulationService.GetVelocidadMaxima(progModificada.Idproducto, progModificada.Idlinea.Value, cancellationToken);
            }

            if (progModificada.Idproducto != null)
            {
                progModificada.Producto = progModificada.PesoMin.HasValue && progModificada.Peso.HasValue
                    ? $"{_contextProg.TablaProductos.FirstOrDefault(o => o.Idproducto == progModificada.Idproducto).Denominacion} Peso: {progModificada.PesoMin} - {progModificada.Peso} g/m2"
                    : $"{_contextProg.TablaProductos.FirstOrDefault(o => o.Idproducto == progModificada.Idproducto).Denominacion}";
            }

            _contextProg.Entry(prog).CurrentValues.SetValues(progModificada);
            var dbResult = await _contextProg.SaveChangesAsync(cancellationToken);
            result.Data = dbResult;
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: EditarPosicionProgramacionCommand - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }
        return result;
    }
}