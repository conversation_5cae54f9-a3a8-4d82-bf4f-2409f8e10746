.navbar-toggler {
    background-color: rgba(255, 255, 255, 0.1);
}

.top-row {
    height: 3.5rem;
    background-color: rgba(0,0,0,0.4);
}

.navbar-brand {
    font-size: 1.1rem;
}

.oi {
    width: 2rem;
    font-size: 1.1rem;
    vertical-align: text-top;
    top: -2px;
}

.nav-item {
    font-size: 0.9rem;
    padding-bottom: 0.5rem;
}

    .nav-item:first-of-type {
        padding-top: 1rem;
    }

    .nav-item:last-of-type {
        padding-bottom: 1rem;
    }

    .nav-item ::deep .nav-link {
        padding: 0.5rem 5px !important;
        cursor: pointer;
    }

    .nav-item ::deep a {
        color: #d7d7d7;
        border-radius: 4px;
        height: 3rem;
        display: flex;
        align-items: center;
        line-height: 3rem;
    }

        .nav-item ::deep a.active {
            background-color: rgba(255,255,255,0.25);
            color: white;
        }

        .nav-item ::deep a:hover {
            background-color: rgba(255,255,255,0.1);
            color: white;
        }

/*-------*/

.sidebar {
    min-width: 181px;
    max-width: 181px;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.16);
    transition: transform 0.1s ease-out;
    height: 100%;
    max-height: 100%;
    min-height: 100%;
/*    overflow: auto;
    overflow-y: auto;*/
    /*background-color: inherit;*/
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

nav a span {
    line-height: 1rem !important;
}

.sidebar.collapse2 {
    /*display: none;*/
    min-width: 60px !important;
    max-width: 60px !important;
}

.sidebar.expand {
    display: block;
}

span.collapse2 {
    display: none !important;
}

@media (max-width: 1199.98px) {
    .sidebar {
        display: none;
    }

        .sidebar.expand {
            position: fixed;
            top: 3.5rem;
            left: 0;
            height: auto;
            min-width: 100%;
            z-index: 1050;
        }
}

::deep .app-sidebar > .nav-pills > .nav-item:first-of-type {
    padding-top: 1rem;
}

::deep .app-sidebar > .nav-pills > .nav-item:last-of-type {
    padding-bottom: 1rem;
}

::deep .app-sidebar .nav-pills > .nav-item a {
    border-radius: 0px;
    display: flex;
    align-items: center;
}

::deep .app-sidebar > .nav-pills > .nav-item > a {
    font-size: 1rem !important;
    font-weight: 600 !important;
    padding: .25rem 1rem .25rem .125rem;
}

    ::deep .app-sidebar,
    ::deep .app-sidebar > .nav-pills,
    ::deep .app-sidebar > .nav-pills > .nav-item,
    ::deep .app-sidebar > .nav-pills > .nav-item > a:not(.active) {
        background-color: inherit;
    }

@media (max-width: 1199.98px) {
    ::deep .app-sidebar > .nav-pills > .nav-item:last-of-type {
        padding-bottom: 0;
    }
}




/*.navbar-toggler {
    background-color: rgba(255, 255, 255, 0.1);
}

.top-row {
    height: 3.5rem;
    background-color: rgba(0,0,0,0.4);
}

.navbar-brand {
    font-size: 1.1rem;
}

.oi {
    width: 2rem;
    font-size: 1.1rem;
    vertical-align: text-top;
    top: -2px;
}

.nav-item {
    font-size: 0.9rem;
    padding-bottom: 0.5rem;
}

    .nav-item:first-of-type {
        padding-top: 1rem;
    }

    .nav-item:last-of-type {
        padding-bottom: 1rem;
    }

    .nav-item ::deep a {
        color: #d7d7d7;
        border-radius: 4px;
        height: 3rem;
        display: flex;
        align-items: center;
        line-height: 3rem;
    }

.nav-item ::deep a.active {
    background-color: rgba(255,255,255,0.25);
    color: white;
}

.nav-item ::deep a:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
}

@media (min-width: 641px) {
    .navbar-toggler {
        display: none;
    }

    .collapse {*/
        /* Never collapse the sidebar for wide screens */
        /*display: block;
    }
    
    .nav-scrollable {*/
        /* Allow sidebar to scroll for tall menus */
        /*height: calc(100vh - 3.5rem);
        overflow-y: auto;
    }
}*/
