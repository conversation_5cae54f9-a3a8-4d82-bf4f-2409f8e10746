﻿@using ProgramadorGeneralBLZ.Shared
@using ProgramadorGeneralBLZ.Shared.DTO

<div class="group-container">
	<DxFormLayoutGroup CssClass="mt-0">
		<HeaderTemplate>
		</HeaderTemplate>
		<Items>
			<!-- SUBCABECERA: TÍTULOS DE COLUMNAS (Cliente, Pedido, Hojalata, etc.) -->
			<DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" CssClass="subcabeceraGrupo">
				<DxFormLayoutItem ColSpanLg="1" BeginRow="true">
					<span class="cabecera-grupo">
						<h3>Cliente</h3>
					</span>
				</DxFormLayoutItem>
				<DxFormLayoutItem ColSpanLg="2">
					<span class="cabecera-grupo">
						<h3>Pedido</h3>
					</span>
				</DxFormLayoutItem>
				<DxFormLayoutItem ColSpanLg="2">
					<span class="cabecera-grupo">
						<h3>Hojalata</h3>
					</span>
				</DxFormLayoutItem>
				<DxFormLayoutItem ColSpanLg="1">
					<span class="cabecera-grupo">
						<h3>Hojas</h3>
					</span>
				</DxFormLayoutItem>
				<DxFormLayoutItem ColSpanLg="1">
					<span class="cabecera-grupo">
						<h3>Diámetro</h3>
					</span>
				</DxFormLayoutItem>
				<DxFormLayoutItem ColSpanLg="4">
					<span class="cabecera-grupo">
						<h3>Tintas</h3>
					</span>
				</DxFormLayoutItem>
			</DxFormLayoutGroup>

			<!-- CUERPO: Iteración de registros -->
			@foreach (var item in ListaPedidos)
			{
				<DxFormLayoutGroup CssClass="@(item.NombreEstado?.ToLower().Trim() ?? Enums.ProgramacionesPantalla_EstadosPedido.SinEmpezar.ToString().ToLower())"
								   Id="@item.Idpedido.ToString()"
								   Decoration="FormLayoutGroupDecoration.None">
					<DxFormLayoutGroup ColSpanLg="12" Decoration="FormLayoutGroupDecoration.None">
						<DxFormLayoutGroup ColSpanLg="7" Decoration="FormLayoutGroupDecoration.None">
							<DxFormLayoutGroup ColSpanLg="12" Decoration="FormLayoutGroupDecoration.None">
								<DxFormLayoutItem ColSpanLg="2">
									@* Nombre Cliente *@
									<span class="">
										@item.IdCliente, @item.NombreCliente
									</span>
								</DxFormLayoutItem>
								<DxFormLayoutItem ColSpanLg="2">
									@* Pedido *@
									<span class="grande negrita" style="padding: 0 0 0 10px;">
										@($"{item.Idpedido:00-0-0000}")
									</span>
								</DxFormLayoutItem>
								<DxFormLayoutItem ColSpanLg="4">
									@* Caract Hojalata *@
									<span class="">
										@item.CaractHjlta
									</span>
								</DxFormLayoutItem>
								<DxFormLayoutItem ColSpanLg="1">
									@* Hojas *@
									<span class="negrita" style="padding: 0 0 0 20px;">
										@($"{item.HojasPedido:N0}")
									</span>
								</DxFormLayoutItem>
								<DxFormLayoutItem ColSpanLg="2">
									@* Plano *@
									<span class="pequeño">
										<span class="textoAzul negrita">Plano: </span>@item.Plano
									</span>
								</DxFormLayoutItem>
							</DxFormLayoutGroup>
							<DxFormLayoutGroup ColSpanLg="12" Decoration="FormLayoutGroupDecoration.None">
								<DxFormLayoutItem ColSpanLg="5" CssClass="borde p-0">
									@* Caja blanca *@
									<div class="" style="white-space: pre-wrap;">
										@item.Rodillo
									</div>
								</DxFormLayoutItem>
								<DxFormLayoutItem ColSpanLg="3">
									@* Min Hojas *@
									<span class="">
										<span class="textoAzul pequeño negrita">Min. Hojas: </span>@item.MinHojas
									</span>
								</DxFormLayoutItem>
								<DxFormLayoutItem ColSpanLg="4">
									@* Formato *@
									<span class="">
										<span class="textoAzul pequeño negrita">Formato: </span>
										@if (item.Formato != null)
										{
											@($"{item.Formato.Value:F2}")
										}
									</span>
								</DxFormLayoutItem>
							</DxFormLayoutGroup>
							<DxFormLayoutItem ColSpanLg="12">
								@* Motivo *@
								<span class="grande">
									<span class="textoAzul  negrita">Motivo: </span>@item.Motivo
								</span>
							</DxFormLayoutItem>
							<DxFormLayoutItem ColSpanLg="12">
								@* Observaciones *@
								<span class="">
									@($"{item.Observaciones} {item.Obspaseposterior} {item.ObsCalidad}")
								</span>
								@if (!string.IsNullOrEmpty(item.NotaJefeTurno))
								{
									<br />
									<div class="noOkRojo" style="white-space: pre-wrap;">
										<span class="negrita">NOTA: </span>@item.NotaJefeTurno
									</div>
								}
							</DxFormLayoutItem>
						</DxFormLayoutGroup>
						<DxFormLayoutGroup ColSpanLg="5" Decoration="FormLayoutGroupDecoration.None">
							@* TINTAS *@
							<DxFormLayoutGroup ColSpanLg="6" Decoration="FormLayoutGroupDecoration.None">
								@foreach (var tuplaTintas in GetListaTintas_T01_T06(item))
								{
									<DxFormLayoutGroup ColSpanLg="12" Decoration="FormLayoutGroupDecoration.None">
										<DxFormLayoutItem ColSpanLg="6"><span class=""> @tuplaTintas.T </span></DxFormLayoutItem>
										<DxFormLayoutItem ColSpanLg="6"><span class=""> @tuplaTintas.FT </span></DxFormLayoutItem>
									</DxFormLayoutGroup>
								}
							</DxFormLayoutGroup>
							<DxFormLayoutGroup ColSpanLg="6" Decoration="FormLayoutGroupDecoration.None">
								@{
									var mostrarT07 = GetSiMostrarTintaExt(item.Idaplicacion, item.T07Ext);
									var mostrarT08 = GetSiMostrarTintaExt(item.Idaplicacion, item.T08Ext);
									var mostrarT09 = GetSiMostrarTintaExt(item.Idaplicacion, item.T09Ext);
									var mostrarT10 = GetSiMostrarTintaExt(item.Idaplicacion, item.T10Ext);
									var mostrarT11 = GetSiMostrarTintaExt(item.Idaplicacion, item.T11Ext);
								}
								<DxFormLayoutGroup ColSpanLg="12" Decoration="FormLayoutGroupDecoration.None">
									<DxFormLayoutItem ColSpanLg="6"><span class="ajusteParaVacio"> @(mostrarT07 ? item.T07Ext : "")</span></DxFormLayoutItem>
									<DxFormLayoutItem ColSpanLg="4"><span class="ajusteParaVacio"> @(mostrarT07 ? item.FT07Ext : "")</span></DxFormLayoutItem>
									<DxFormLayoutItem ColSpanLg="2"><span class="negrita">@(item.Flejar != null && item.Flejar.Value ? "SI" : "NO")</span></DxFormLayoutItem>
								</DxFormLayoutGroup>
								<DxFormLayoutGroup ColSpanLg="12" Decoration="FormLayoutGroupDecoration.None">
									<DxFormLayoutItem ColSpanLg="6"><span class="ajusteParaVacio">@(mostrarT08 ? item.T08Ext : "")</span></DxFormLayoutItem>
									<DxFormLayoutItem ColSpanLg="6"><span class="ajusteParaVacio">@(mostrarT08 ? item.FT08Ext : "")</span></DxFormLayoutItem>
								</DxFormLayoutGroup>
								<DxFormLayoutGroup ColSpanLg="12" Decoration="FormLayoutGroupDecoration.None">
									<DxFormLayoutItem ColSpanLg="6"><span class="ajusteParaVacio">@(mostrarT09 ? item.T09Ext : "")</span></DxFormLayoutItem>
									<DxFormLayoutItem ColSpanLg="6"><span class="ajusteParaVacio">@(mostrarT09 ? item.FT09Ext : "")</span></DxFormLayoutItem>
								</DxFormLayoutGroup>
								<DxFormLayoutGroup ColSpanLg="12" Decoration="FormLayoutGroupDecoration.None">
									<DxFormLayoutItem ColSpanLg="4"><span class="ajusteParaVacio">@(mostrarT10 ? item.T10Ext : "")</span></DxFormLayoutItem>
									<DxFormLayoutItem ColSpanLg="4"><span class="ajusteParaVacio">@(mostrarT10 ? item.FT10Ext : "")</span></DxFormLayoutItem>
									<DxFormLayoutItem CssClass="" ColSpanLg="4">
										<div class="btn-group btn-group-sm" role="group" aria-label="Estados del pedido" style="border-radius: 0.375rem; overflow: hidden;">
											<button type="button"
											        class="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Empezado)"
											        title="Empezar orden"
											        disabled="@(!EsMaquina)"
											        @onclick="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Empezado))">
												<i class="oi oi-media-play"></i>
											</button>

											<button type="button"
											        class="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Detenido)"
											        title="Detener orden"
											        disabled="@(!EsMaquina)"
											        @onclick="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Detenido))">
												<i class="oi oi-media-stop"></i>
											</button>

											<button type="button"
											        class="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Terminado)"
											        title="Finalizar orden"
											        disabled="@(!EsMaquina)"
											        @onclick="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Terminado))">
												<i class="oi oi-circle-check"></i>
											</button>

											<button type="button"
											        class="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Retirado)"
											        title="Sacar orden"
											        disabled="@(!EsMaquina)"
											        @onclick="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Retirado))">
												<i class="oi oi-circle-x"></i>
											</button>
										</div>
									</DxFormLayoutItem>
								</DxFormLayoutGroup>
								<DxFormLayoutGroup ColSpanLg="12" Decoration="FormLayoutGroupDecoration.None">
									<DxFormLayoutItem ColSpanLg="4"><span class="ajusteParaVacio">@(mostrarT11 ? item.T11Ext : "")</span></DxFormLayoutItem>
									<DxFormLayoutItem ColSpanLg="4"><span class="ajusteParaVacio">@(mostrarT11 ? item.FT11Ext : "")</span></DxFormLayoutItem>
									<DxFormLayoutItem CssClass="" ColSpanLg="4">
										<DxButton Click="@(async () => await SetDatosPopUp(item, "Rodillo"))" Context="btn1"
										          Visible=@(EsRodillos) RenderStyle="ButtonRenderStyle.Info" CssClass="btnGroup" Text="Rodillo"/>
										<DxButton Click="@(async () => await SetDatosPopUp(item, "Nota"))" Context="btn2"
										          Visible=@(EsEncargado || EsJefeTurno) RenderStyle="ButtonRenderStyle.Info" CssClass="btnGroup" Text="Nota"/>
									</DxFormLayoutItem>
								</DxFormLayoutGroup>
							</DxFormLayoutGroup>
						</DxFormLayoutGroup>
					</DxFormLayoutGroup>
					<DxFormLayoutGroup ColSpanLg="12" Decoration="FormLayoutGroupDecoration.None" Visible="@(item.ApliSimul is not null)">
						<DxFormLayoutItem ColSpanLg="12">
							<span class="grande">
								<span class="textoAzul negrita">Aplicación Simultanea: </span>@item.ApliProducto
							</span>
						</DxFormLayoutItem>
						<DxFormLayoutItem ColSpanLg="12">
							<span class="grande">
								<span class="textoAzul negrita">Forma Flejado: </span>@item.FormaFlejado
							</span>
						</DxFormLayoutItem>
					</DxFormLayoutGroup>
					<DxFormLayoutGroup ColSpanLg="12" Decoration="FormLayoutGroupDecoration.None">
						<DxFormLayoutItem ColSpanLg="3">
							<span class="">
								@item.CogerDe
							</span>
						</DxFormLayoutItem>
						<DxFormLayoutItem ColSpanLg="1">
							<span class="grande">
								<span class="textoAzul negrita">Máculas: </span>@(item.Maculas != null && item.Maculas.Value ? "SI" : "NO")
							</span>
						</DxFormLayoutItem>
						<DxFormLayoutItem ColSpanLg="3">
							<span class="">
								@item.TextoEstadoCodApli
							</span>
						</DxFormLayoutItem>
						<DxFormLayoutItem ColSpanLg="1" Visible="@(item.Urgente != null && item.Urgente.Value)">
							<span class="negrita">
								URGENTE
							</span>
						</DxFormLayoutItem>
						<DxFormLayoutItem ColSpanLg="1">
							<span class="negrita">
								@(item.RequeridoEnFecha.HasValue? item.RequeridoEnFecha.Value.ToString("dd/MM") : string.Empty)
							</span>
						</DxFormLayoutItem>
						<DxFormLayoutItem ColSpanLg="1">
							<span class="">
								@(item.HoraComienzoEstimada.Value.ToString("dd/MM - HH:mm"))
							</span>
						</DxFormLayoutItem>
						<DxFormLayoutItem ColSpanLg="1">
							<span class="">
								@(item.HoraFinEstimada.Value.ToString("dd/MM - HH:mm"))
							</span>
						</DxFormLayoutItem>
						<DxFormLayoutItem ColSpanLg="1">
							<span class="grande negrita">
								@item.Posicion
							</span>
						</DxFormLayoutItem>
					</DxFormLayoutGroup>
					<!-- Línea separadora / Borde inferior -->
					<DxFormLayoutGroup ColSpanLg="12" Decoration="FormLayoutGroupDecoration.None">
						<Items>
							<DxFormLayoutItem ColSpanLg="12" CssClass="borde">
								<span style="width: 100%; display:block;"></span>
							</DxFormLayoutItem>
						</Items>
					</DxFormLayoutGroup>
				</DxFormLayoutGroup>
			}

			<!-- SUMARIO O PIE DEL BLOQUE -->
			@if (ListaPedidos.Any())
			{
				<span class="sumario">
					<span>@($"{ListaPedidos.Min(item => item.Posicion):F0} - {ListaPedidos.Max(item => item.Posicion):F0}")</span>
					<span>Hojas: @($"{ListaPedidos.Sum(item => item.HojasPedido):N0}")</span>
					<span>Superficie: @($"{ListaPedidos.Sum(item => item.Sup):N2}") m2</span>
					<span>
						Fin: @(ListaPedidos.Where(o => o.HoraFinEstimada != null)
											.Max(item => item.HoraFinEstimada.Value)
											.ToString("dd/MM/yyyy hh:mm"))
				</span>
				<span>Barniz: @($"{ListaPedidos.Where(o => o.BarnizNecesario.HasValue).Sum(item => item.BarnizNecesario):N2}") kg</span>
			</span>
						}
		</Items>
	</DxFormLayoutGroup>
</div>

@code {
	[Parameter] public List<PedidoProgramacionEnPantallaDTO> ListaPedidos { get; set; }
	[Parameter] public string Cabecera { get; set; }
	// Callbacks para comunicación con el componente padre
	[Parameter] public Func<PedidoProgramacionEnPantallaDTO, string, string, Task> OnObservacionUpdated { get; set; }
	[Parameter] public Func<PedidoProgramacionEnPantallaDTO, Enums.ProgramacionesPantalla_EstadosPedido, Task> OnEstadoPedidoUpdatedViaHub { get; set; }
	[Parameter] public Func<PedidoProgramacionEnPantallaDTO, string, Task> OnShowPopup { get; set; }

	// Propiedades de roles pasadas desde el padre
	[Parameter] public bool EsJefeTurno { get; set; }
	[Parameter] public bool EsEncargado { get; set; }
	[Parameter] public bool EsRodillos { get; set; }
	[Parameter] public bool EsMaquina { get; set; }

	// Diccionario que mapea idPedido al estado actual
	private Dictionary<int, string> _estados { get; set; }

	// Método para cambiar el estado de un pedido
	private async Task CambiarEstado(PedidoProgramacionEnPantallaDTO pedido, Enums.ProgramacionesPantalla_EstadosPedido nuevoEstado)
	{
		await OnEstadoPedidoUpdatedViaHub(pedido, nuevoEstado);
	}

	// Método para obtener las clases CSS del botón según el estado
	private string GetButtonClass(PedidoProgramacionEnPantallaDTO item, Enums.ProgramacionesPantalla_EstadosPedido estado)
	{
		var baseClass = "btn btn-sm";
		var isSelected = item.IdEstado == (int)estado;

		if (isSelected)
		{
			return $"{baseClass} btn-info";
		}
		else
		{
			// Fondo blanco para todos los botones no seleccionados
			return $"{baseClass} btn-light";
		}
	}

	private async Task SetDatosPopUp(PedidoProgramacionEnPantallaDTO pedido, string texto)
	{
		await OnShowPopup(pedido, texto);
	}

	private List<(string T, string FT)> GetListaTintas_T01_T06(PedidoProgramacionEnPantallaDTO pedido)
	{
		List<(string T, string FT)> lista = new();

		if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T01Ext))
			lista.Add((pedido.T01Ext, pedido.FT01Ext));
		else if (GetSiMostrarTintaInt(pedido.Idaplicacion))
			lista.Add((pedido.T01Int, string.Empty));
		else
			lista.Add(("", ""));


		if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T02Ext))
			lista.Add((pedido.T02Ext, pedido.FT02Ext));
		else if (GetSiMostrarTintaInt(pedido.Idaplicacion)) 
			lista.Add((pedido.T02Int, string.Empty));
		else
			lista.Add(("", ""));


		if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T03Ext))
			lista.Add((pedido.T03Ext, pedido.FT03Ext));
		else if (GetSiMostrarTintaInt(pedido.Idaplicacion))
			lista.Add((pedido.T03Int, string.Empty));
		else
			lista.Add(("", ""));


		if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T04Ext))
			lista.Add((pedido.T04Ext, pedido.FT04Ext));
		else if (GetSiMostrarTintaInt(pedido.Idaplicacion))
			lista.Add((pedido.T04Int, string.Empty));
		else
			lista.Add(("", ""));


		if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T05Ext))
			lista.Add((pedido.T05Ext, pedido.FT05Ext));
		else
			lista.Add(("", ""));


		if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T06Ext))
			lista.Add((pedido.T06Ext, pedido.FT06Ext));
		else
			lista.Add(("", ""));

		return lista;
	}

	private bool GetSiMostrarTintaExt(int? idAplicacion, string? T)
	{
		// las tintas exteriores únicamente pueden ir con esta config
		return idAplicacion == 999 || (idAplicacion == 997 && T.Contains("1000"));
	}

	private bool GetSiMostrarTintaInt(int? idAplicacion)
	{
		// las tintas interiores únicamente pueden ir con esta config
		return idAplicacion == 999 || idAplicacion == 998; // TODO: preguntar si es necesario la 999
	}
}