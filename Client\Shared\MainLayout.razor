﻿@using ProgramadorGeneralBLZ.Client.Pages.Components

@inject IHttpClientFactory HttpClientFactory
@inherits LayoutComponentBase
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
<DxLayoutBreakpoint MaxWidth="1200"
@bind-IsActive="@IsMobileLayout" />

<div class="page @(esDesarrollo?"fondoDesarrollo":"")">
    <BlazoredToasts MaxToastCount="3" RemoveToastsOnNavigation="true"  ShowProgressBar="true" Timeout="5"/>
    <DxLoadingPanel @bind-Visible="_inactivo"
                    IsContentBlocked="true"
                    CloseOnClick="true" 
                    IndicatorAnimationType="WaitIndicatorAnimationType.Spin"
                    Text="La aplicación está inactiva, por favor recarga la página."
                    ApplyBackgroundShading="true"
                    IndicatorAreaVisible="true"
                    ZIndex="5000">
        <DxGridLayout CssClass="page-layout">
            <Rows>
                @if (IsMobileLayout)
                {
                    @*<DxGridLayoutRow Areas="header" Height="auto"></DxGridLayoutRow>*@
                    <DxGridLayoutRow Areas="sidebar" Height="auto"></DxGridLayoutRow>
                    <DxGridLayoutRow Areas="content" />
                }
                else
                {
                    @*<DxGridLayoutRow Areas="header header" Height="auto"/>*@
                    <DxGridLayoutRow Areas="sidebar content" />
                    @*<DxGridLayoutRow Areas="@(IsSidebarExpanded ? "sidebar content" : "content content")" />*@
                }
            </Rows>
            <Columns>
                @if (!IsMobileLayout)
                {
                    <DxGridLayoutColumn Width="auto" />
                    <DxGridLayoutColumn />
                }
            </Columns>
            <Items>
                @*            <DxGridLayoutItem Area="header" CssClass="layout-item">
                <Template>
                <Header @bind-ToggleOn="@IsSidebarExpanded"/>
                </Template>
                </DxGridLayoutItem>*@
                <DxGridLayoutItem Area="sidebar" CssClass="layout-item">
                    <Template>
                        <NavMenu StateCssClass="@NavMenuCssClass" />
                    </Template>
                </DxGridLayoutItem>
                <DxGridLayoutItem Area="content" CssClass="content px-1 py-0 layout-item">
                    <Template>
                        @Body

                        <SpinnerV2></SpinnerV2>
                    </Template>
                </DxGridLayoutItem>
            </Items>
        </DxGridLayout>
    </DxLoadingPanel>
</div>

@code {
    bool _inactivo = false;
    string? NavMenuCssClass = "collapse2";
    bool _isMobileLayout;
    private bool esDesarrollo;
    bool IsMobileLayout
    {
        get => _isMobileLayout;
        set
        {
            _isMobileLayout = value;
            IsSidebarExpanded = !_isMobileLayout;
        }
    }

    bool _isSidebarExpanded = false;
    bool IsSidebarExpanded
    {
        get => _isSidebarExpanded;
        set
        {
            if (_isSidebarExpanded != value)
            {
                NavMenuCssClass = value ? "expand" : "collapse2";
                //NavMenuCssClass = value ? "expand" : "collapse";
                _isSidebarExpanded = value;
            }
        }
    }
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            var dotNetReference = DotNetObjectReference.Create(this);
            await JSRuntime.InvokeVoidAsync("startInactivityTimer", dotNetReference);
        }

    }
    protected override async Task OnInitializedAsync()
    {
        NavigationManager.LocationChanged += OnLocationChanged;
        var client = HttpClientFactory.CreateClient("DatabaseAPI");
        esDesarrollo = Convert.ToBoolean(await client.GetStringAsync("ConsultaDB/current-database"));

    }
    async void OnLocationChanged(object? sender, LocationChangedEventArgs args)
    {
        if (IsMobileLayout)
        {
            IsSidebarExpanded = false;
            await InvokeAsync(StateHasChanged);
        }
    }
    [JSInvokable]
    public void SetInactivo(bool valor)
    {
        _inactivo = valor;
        InvokeAsync(StateHasChanged); // Para asegurar que la UI se actualice
    }
    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }
}